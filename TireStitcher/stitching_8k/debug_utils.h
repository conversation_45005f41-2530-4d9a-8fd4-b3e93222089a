#ifndef DEBUG_UTILS_H
#define DEBUG_UTILS_H

#include <string>
#include <sstream>
#include <iomanip>
#include <opencv2/opencv.hpp>  // Benötigt für cv::Mat
#include "config.h"

// Diese Funktion inline deklarieren, damit sie in mehreren Dateien verwendet werden kann
inline void debugFloatingPoint(const std::string& label, double value) {
    std::stringstream ss;
    ss << std::fixed << std::setprecision(12); // Maximale Präzision für Debugging
    ss << "DEBUG FLOAT [" << label << "]: " << value;
    
    // Typinformationen anzeigen
    ss << " (size: " << sizeof(value) << " bytes)";
    
    // Bit-Repräsentation für tieferes Debugging
    union {
        double d;
        uint64_t i;
    } u;
    u.d = value;
    
    ss << " hex: 0x" << std::hex << u.i;
    
    safePrint(ss.str(), true);
}

// Debugging-Funktion für Optical Flow Matrix mit zusätzlichen Details
inline void debugFlowMatrix(const cv::Mat& flow, const std::string& tag, bool verbose) {
    if (!verbose) return;
    
    if (flow.empty()) {
        safePrint(tag + ": Flow matrix is empty", true);
        return;
    }
    
    // Extrahiere die X-Komponente des Flusses
    std::vector<cv::Mat> flowComponents;
    cv::split(flow, flowComponents);
    
    if (flowComponents.empty()) {
        safePrint(tag + ": No flow components extracted", true);
        return;
    }
    
    // Statistiken für die X-Komponente
    cv::Scalar mean, stddev;
    cv::meanStdDev(flowComponents[0], mean, stddev);
    
    // Finde min/max Werte
    double minVal, maxVal;
    cv::minMaxLoc(flowComponents[0], &minVal, &maxVal);
    
    std::stringstream ss;
    ss << std::fixed << std::setprecision(12); // Erhöhte Präzision für Debug-Ausgabe
    ss << tag << " Flow X-component: "
       << "mean=" << mean[0] 
       << ", stddev=" << stddev[0]
       << ", min=" << minVal
       << ", max=" << maxVal;
    
    // Sammle einige beispielhafte Werte aus der Matrix mit hoher Präzision
    ss << ", samples=[";
    int numSamples = std::min(5, flow.rows);
    for (int i = 0; i < numSamples; i++) {
        int row = flow.rows / (numSamples + 1) * (i + 1);
        int col = flow.cols / 2; // Mittlere Spalte
        if (row < flowComponents[0].rows && col < flowComponents[0].cols) {
            float xValue = flowComponents[0].at<float>(row, col);
            ss << std::setprecision(12) << xValue;
            if (i < numSamples - 1) ss << ", ";
        }
    }
    ss << "]";
    
    safePrint(ss.str(), true);
}

#endif // DEBUG_UTILS_H