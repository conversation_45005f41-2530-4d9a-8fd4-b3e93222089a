#include "strip_extraction.h"
#include "config.h"
#include "debug_utils.h" // Neue Header-<PERSON>i <PERSON>

#include <map>
#include <numeric>
#include <algorithm>
#include <stdexcept>
#include <sstream>
#include <iomanip>

// Global cache for blending masks and other reusable data
std::map<int, cv::Mat> blendingMaskCache;
std::mutex cacheMutex;

cv::Mat getCentralStrip(const cv::Mat& image, int stripWidth) {
    try {
        if (image.empty()) {
            throw std::runtime_error("Input image is empty");
        }
        
        int height = image.rows;
        int width = image.cols;
        
        // Validiere und korrigiere stripWidth
        if (stripWidth <= 0 || stripWidth > width) {
            // Ein Standardwert von 1/3 der Bildbreite ist oft eine gute Wahl
            int newWidth = std::min(width, std::max(30, width / 3));
            safePrint("Warning: Invalid strip width " + std::to_string(stripWidth) + 
                     " adjusted to " + std::to_string(newWidth), true);
            stripWidth = newWidth;
        }
        
        // Berechne den zentralen Bereich
        int center = width / 2;
        int halfStrip = stripWidth / 2;
        int start = center - halfStrip;
        int end = start + stripWidth; // Sicherstellen, dass die Breite exakt stripWidth ist
        
        // Begrenze auf Bildgrenzen - wichtig!
        start = std::max(0, start);
        end = std::min(width, end);
        
        // Überprüfe, ob die resultierende Breite korrekt ist
        int actualWidth = end - start;
        if (actualWidth <= 0) {
            throw std::runtime_error("Invalid strip dimensions after boundary check");
        }
        
        if (actualWidth != stripWidth) {
            safePrint("Note: Adjusted strip width from " + std::to_string(stripWidth) + 
                     " to " + std::to_string(actualWidth) + " due to image boundaries", false);
        }
        
        // Extrahiere den Strip (mit clone() für eigene Kopie)
        cv::Mat strip = image(cv::Rect(start, 0, actualWidth, height)).clone();
        
        // Überprüfe ob die Extraktion erfolgreich war
        if (strip.empty()) {
            throw std::runtime_error("Failed to extract strip from image");
        }
        
        return strip;
    }
    catch (const std::exception& e) {
        safePrint(std::string("Error getting central strip: ") + e.what(), true);
        
        // Fallback: Versuch, einen kleineren Strip zu extrahieren
        if (!image.empty()) {
            int height = image.rows;
            int width = image.cols;
            int center = width / 2;
            int safeWidth = std::min(100, width / 2);
            
            // Sicherstellen, dass die Grenzen gültig sind
            int start = std::max(0, center - safeWidth / 2);
            int end = std::min(width, center + safeWidth / 2);
            int actualWidth = end - start;
            
            if (actualWidth > 0) {
                safePrint("Using fallback strip width: " + std::to_string(actualWidth), true);
                return image(cv::Rect(start, 0, actualWidth, height)).clone();
            }
        }
        
        throw std::runtime_error("Cannot extract strip from image: " + std::string(e.what()));
    }
}

double measureMovement(const cv::Mat& img1, const cv::Mat& img2, const StitchConfig& config) {
    cv::Mat gray1, gray2;
    
    if (img1.channels() == 3) {
        cv::cvtColor(img1, gray1, cv::COLOR_BGR2GRAY);
    } else {
        gray1 = img1.clone();
    }
    
    if (img2.channels() == 3) {
        cv::cvtColor(img2, gray2, cv::COLOR_BGR2GRAY);
    } else {
        gray2 = img2.clone();
    }
    
    int height = gray1.rows;
    int width = gray1.cols;
    
    // Adaptive template size and position based on configuration
    std::vector<double> templateSizes;
    std::vector<double> templatePositions;
    
    // Optimize the number of templates based on desired precision
    switch (config.templateMatchPrecision) {
        case 0: // Low precision (fast)
            templateSizes = {0.15};
            templatePositions = {0.5};
            break;
        case 1: // Medium precision (standard)
            templateSizes = {0.1, 0.2};
            templatePositions = {0.25, 0.75};
            break;
        case 2: // High precision (slow)
        default:
            // Increased number of positions for better coverage
            templateSizes = {0.08, 0.12, 0.16};
            templatePositions = {0.2, 0.35, 0.5, 0.65, 0.8};
            break;
    }
    
    std::vector<double> allMovements;
    std::vector<double> allWeights; // Weight each measurement by its correlation quality
    std::mutex movementsMutex;
    
    // Use OpenMP for parallel template processing
    #pragma omp parallel for collapse(2)
    for (int s = 0; s < templateSizes.size(); s++) {
        for (int p = 0; p < templatePositions.size(); p++) {
            double size = templateSizes[s];
            double pos = templatePositions[p];
            
            int templateWidth = static_cast<int>(width * size);
            if (templateWidth < 10) continue;
            
            // Calculate position with higher precision
            double exactCenterPos = width * pos;
            int centerPos = static_cast<int>(exactCenterPos);
            
            int templateStart = std::max(0, centerPos - templateWidth / 2);
            int templateEnd = std::min(width, templateStart + templateWidth);
            
            if (templateEnd - templateStart < 10) continue;
            
            // Extract template
            cv::Mat templ = gray1(cv::Rect(templateStart, 0, templateEnd - templateStart, height));
            
            // Define search area with extra margin for sub-pixel refinement
            int searchMargin = templateWidth + 5; // Additional margin
            int searchStart = std::max(0, templateStart - searchMargin);
            int searchEnd = std::min(width, templateEnd + searchMargin);
            
            if (searchEnd - searchStart <= templateWidth) continue;
            
            cv::Mat searchArea = gray2(cv::Rect(searchStart, 0, searchEnd - searchStart, height));
            
            try {
                // Template matching
                cv::Mat result;
                cv::matchTemplate(searchArea, templ, result, cv::TM_CCOEFF_NORMED);
                
                // Find initial maximum location
                double maxVal;
                cv::Point maxLoc;
                cv::minMaxLoc(result, nullptr, &maxVal, nullptr, &maxLoc);
                
                // Sub-pixel refinement - only perform if not at the edge
                cv::Point2d refinedMaxLoc = maxLoc;
                
                if (maxLoc.x > 0 && maxLoc.x < result.cols - 1) {
                    // Get values at adjacent positions
                    double left = result.at<float>(maxLoc.y, maxLoc.x - 1);
                    double center = result.at<float>(maxLoc.y, maxLoc.x);
                    double right = result.at<float>(maxLoc.y, maxLoc.x + 1);
                    
                    // Parabolic interpolation
                    double denominator = 2.0 * (left + right - 2.0 * center);
                    if (std::abs(denominator) > 1e-6) {
                        double deltaX = (left - right) / denominator;
                        refinedMaxLoc.x = maxLoc.x + deltaX;
                    }
                }
                
                // Calculate sub-pixel precise movement
                double templateCenter = templateWidth / 2.0;
                double matchCenter = refinedMaxLoc.x + templateCenter;
                double searchCenter = (searchEnd - searchStart) / 2.0;
                
                double movement = matchCenter - searchCenter;
                
                // Keep only good matches with correlation weighting
                if (maxVal > 0.8) {  // Lowered threshold to include more measurements
                    double weight = maxVal * maxVal; // Square for emphasizing good matches
                    
                    #pragma omp critical
                    {
                        allMovements.push_back(movement);
                        allWeights.push_back(weight);
                    }
                }
            }
            catch (const cv::Exception& e) {
                safePrint(std::string("Warning: Template matching failed: ") + e.what(), false, config);
            }
        }
    }
    
    if (allMovements.empty()) {
        safePrint("Warning: Could not measure movement. Using default value.", true);
        return 100;
    }
    
    // Weighted combination of median and weighted average for robust results
    
    // Calculate weighted average
    double sumWeights = 0.0;
    double sumWeightedMovements = 0.0;
    
    for (size_t i = 0; i < allMovements.size(); i++) {
        sumWeightedMovements += allMovements[i] * allWeights[i];
        sumWeights += allWeights[i];
    }
    
    double weightedAverage = sumWeightedMovements / sumWeights;
    
    // Calculate median for robustness
    std::vector<double> sortedMovements = allMovements;
    std::sort(sortedMovements.begin(), sortedMovements.end());
    
    double median;
    if (sortedMovements.size() % 2 == 0) {
        median = (sortedMovements[sortedMovements.size()/2 - 1] + 
                 sortedMovements[sortedMovements.size()/2]) / 2.0;
    } else {
        median = sortedMovements[sortedMovements.size()/2];
    }
    
    // Combine weighted average and median for best results
    // Use median more if there are outliers
    bool hasOutliers = false;
    
    // Check for outliers by comparing the spread
    double q1 = sortedMovements[sortedMovements.size() / 4];
    double q3 = sortedMovements[3 * sortedMovements.size() / 4];
    double iqr = q3 - q1;
    double outlierThreshold = 1.5 * iqr;
    
    for (double val : sortedMovements) {
        if (val < q1 - outlierThreshold || val > q3 + outlierThreshold) {
            hasOutliers = true;
            break;
        }
    }
    
    // Final calculation with proper weighting based on outlier presence
    double finalMovement;
    if (hasOutliers) {
        finalMovement = 0.8 * median + 0.2 * weightedAverage; // Favor median for robustness
    } else {
        finalMovement = 0.4 * median + 0.6 * weightedAverage; // Favor weighted average for precision
    }
    
    // Debug output
    if (config.verbose) {
        std::stringstream ss;
        ss << std::fixed << std::setprecision(6);
        ss << "Movement calculation: weighted average=" << weightedAverage 
           << ", median=" << median 
           << ", final=" << finalMovement
           << ", measurements=" << allMovements.size();
        safePrint(ss.str(), false, config);
    }
    
    return std::abs(finalMovement);
}

cv::Mat calculateOpticalFlow(const cv::Mat& prevImg, const cv::Mat& nextImg) {
    cv::Mat prevGray, nextGray;
    
    // Convert to grayscale for optical flow
    if (prevImg.channels() == 3) {
        cv::cvtColor(prevImg, prevGray, cv::COLOR_BGR2GRAY);
    } else {
        prevGray = prevImg.clone();
    }
    
    if (nextImg.channels() == 3) {
        cv::cvtColor(nextImg, nextGray, cv::COLOR_BGR2GRAY);
    } else {
        nextGray = nextImg.clone();
    }
    
    // Verbesserte Vorverarbeitung für Subpixel-Genauigkeit
    
    // 1. Gaußscher Filter mit kleinerem Kernel für besseres Detail-Rausch-Verhältnis
    cv::GaussianBlur(prevGray, prevGray, cv::Size(5, 5), 0);
    cv::GaussianBlur(nextGray, nextGray, cv::Size(5, 5), 0);
    
    // 2. Kontrastverstärkung für bessere Kantenerkennung
    cv::Ptr<cv::CLAHE> clahe = cv::createCLAHE(2.0, cv::Size(8, 8));
    cv::Mat prevEnhanced, nextEnhanced;
    clahe->apply(prevGray, prevEnhanced);
    clahe->apply(nextGray, nextEnhanced);
    
    // Initialize flow matrix
    cv::Mat flow(prevEnhanced.size(), CV_32FC2);
    
    // Optimized parameters for Farneback method for subpixel precision
    cv::calcOpticalFlowFarneback(
        prevEnhanced, nextEnhanced,
        flow,
        0.5,      // Pyramid scale - kleinerer Wert für höhere Genauigkeit
        7,        // Levels - bleibt bei 5 für Balance zwischen Präzision und Performance
        21,       // Window size - vergrößert für stabilere Ergebnisse
        15,        // Iterations - bleibt bei 3 für angemessene Performance
        7,        // Polynom-Grad - bleibt bei 5 als Standardwert
        1.1,      // Sigma - leicht reduziert für schärfere Kanten
        cv::OPTFLOW_FARNEBACK_GAUSSIAN  // Flags - Gaußsche Wichtung für bessere Präzision
    );
    
    return flow;
}

double extractMovementFromFlow(const cv::Mat& flow, bool horizontal, bool verbose) {
    // Prüfen Sie, ob der Fluss leer ist
    if (flow.empty()) {
        safePrint("ERROR: Flow matrix is empty in extractMovementFromFlow", true);
        return 0.0;
    }

    // Extrahieren Sie den horizontalen oder vertikalen Fluss
    cv::Mat flowComponent;
    if (horizontal) {
        // Horizontale Komponente (x-Richtung)
        std::vector<cv::Mat> flowChannels;
        cv::split(flow, flowChannels);
        
        // Sicherheitsüberprüfung
        if (flowChannels.size() < 2) {
            safePrint("ERROR: Flow matrix does not have enough channels", true);
            return 0.0;
        }
        
        flowComponent = flowChannels[0]; // x-Komponente
        
        if (verbose) {
            safePrint("Extracted horizontal flow component, shape: " + 
                      std::to_string(flowComponent.rows) + "x" + 
                      std::to_string(flowComponent.cols) + 
                      ", type: " + std::to_string(flowComponent.type()), true);
        }
    } else {
        // Vertikale Komponente (y-Richtung)
        std::vector<cv::Mat> flowChannels;
        cv::split(flow, flowChannels);
        
        // Sicherheitsüberprüfung
        if (flowChannels.size() < 2) {
            safePrint("ERROR: Flow matrix does not have enough channels", true);
            return 0.0;
        }
        
        flowComponent = flowChannels[1]; // y-Komponente
    }

    // Statistiken der Komponente berechnen
    cv::Scalar mean, stddev;
    cv::meanStdDev(flowComponent, mean, stddev);
    
    if (verbose) {
        std::stringstream ss;
        ss << std::fixed << std::setprecision(12);
        ss << "Flow component statistics: mean=" << mean[0] 
           << ", stddev=" << stddev[0];
        safePrint(ss.str(), true);
    }

    // Sammle alle gültigen Flusswerte mit ihrer Zuverlässigkeit
    std::vector<double> flowValues;
    std::vector<double> flowWeights;  // Gewichte für jeden Wert
    
    // Zentralbereich des Bildes definieren (mehr Vertrauen in zentrale Pixel)
    double centerX = flowComponent.cols / 2.0;
    double centerY = flowComponent.rows / 2.0;
    double maxRadius = std::sqrt(centerX*centerX + centerY*centerY);
    
    double totalWeight = 0.0;
    double weightedSum = 0.0;
    
    for (int y = 0; y < flowComponent.rows; y++) {
        for (int x = 0; x < flowComponent.cols; x++) {
            float value = flowComponent.at<float>(y, x);
            
            // Filtern Sie Ausreißer und ungültige Werte heraus
            if (!std::isnan(value) && !std::isinf(value) && std::abs(value) < 1000.0) {
                double distance = std::sqrt((x - centerX)*(x - centerX) + (y - centerY)*(y - centerY));
                double distanceWeight = 1.0 - (distance / maxRadius) * 0.7;  // Zentrale Pixel haben mehr Einfluss
                
                // Geringeres Gewicht für extreme Werte
                double valueWeight = 1.0 / (1.0 + 0.1 * std::abs(value));
                
                // Kombiniertes Gewicht
                double weight = distanceWeight * valueWeight;
                
                // Speichern des Wertes und Gewichts
                flowValues.push_back(static_cast<double>(value));
                flowWeights.push_back(weight);
                
                // Aktualisieren der gewichteten Summe
                weightedSum += value * weight;
                totalWeight += weight;
            }
        }
    }

    // Wenn keine gültigen Werte vorhanden sind, geben Sie 0 zurück
    if (flowValues.empty()) {
        safePrint("WARNING: No valid flow values found", true);
        return 0.0;
    }

    if (verbose) {
        safePrint("Collected " + std::to_string(flowValues.size()) + " valid flow values", true);
        
        // Ausgabe einiger Beispielwerte
        if (!flowValues.empty()) {
            std::stringstream ss;
            ss << std::fixed << std::setprecision(12);
            ss << "Sample flow values: [";
            for (int i = 0; i < std::min(10, static_cast<int>(flowValues.size())); i++) {
                ss << flowValues[i];
                if (i < std::min(9, static_cast<int>(flowValues.size())-1)) ss << ", ";
            }
            ss << "]";
            safePrint(ss.str(), true);
        }
    }

    // Berechne den gewichteten Mittelwert für Subpixel-Genauigkeit
    double weightedMean = weightedSum / totalWeight;
    
    // Sortieren Sie die Werte, um den Median zu finden (für Robustheit)
    std::vector<double> sortedValues = flowValues;
    std::sort(sortedValues.begin(), sortedValues.end());
    
    // Berechnen Sie den Median
    double median;
    size_t size = sortedValues.size();
    if (size % 2 == 0) {
        median = (sortedValues[size/2 - 1] + sortedValues[size/2]) / 2.0;
    } else {
        median = sortedValues[size/2];
    }

    // Kombination aus gewichtetem Mittelwert und Median für robuste Subpixel-Genauigkeit
    double finalMovement;
    
    // Wenn Median und gewichteter Mittelwert nahe beieinander liegen, bevorzuge den gewichteten Mittelwert
    if (std::abs(median - weightedMean) < stddev[0] * 0.5) {
        finalMovement = weightedMean;
        if (verbose) safePrint("Using weighted mean for subpixel precision", true);
    } else {
        // Bei großen Abweichungen: Kombination aus beiden mit Schwerpunkt auf dem robusteren Median
        finalMovement = median * 0.7 + weightedMean * 0.3;
        if (verbose) safePrint("Using robust combined approach (median dominant)", true);
    }

    // Debugging des Median-Werts
    if (verbose) {
        std::stringstream ss;
        ss << std::fixed << std::setprecision(12);
        ss << "Subpixel analysis: weighted mean=" << weightedMean 
           << ", median=" << median 
           << ", final value=" << finalMovement;
        safePrint(ss.str(), true);
        
        // Debug-Ausgabe des absoluten Median-Werts
        debugFloatingPoint("finalMovement", finalMovement);
        debugFloatingPoint("abs(finalMovement)", std::abs(finalMovement));
    }

    // Geben Sie den absoluten Wert zurück (Richtung ist nicht wichtig)
    return std::abs(finalMovement);
}