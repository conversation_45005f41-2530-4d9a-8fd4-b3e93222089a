#include "panorama_processor.h"
#include "image_loader.h"
#include "strip_extraction.h"
#include "movement_detection.h"
#include "image_blending.h"

#include <chrono>
#include <deque>
#include <fstream>
#include <numeric>
#include <algorithm>
#include <thread>
#include <mutex>
#include <direct.h>  // For _mkdir on Windows
#include <sys/stat.h> // For stat

// Include frame_extraction_utils.h which has the directory handling functions
#include "frame_extraction_utils.h"

// Debug-Funktion zum Speichern von Zwischenständen des Panoramas für Diagnose
void debugPanorama(const cv::Mat& panorama, const std::string& outputFolder,
                  const std::string& label, int frameIdx, const StitchConfig& config) {
    // Nur wenn verbose aktiviert ist
    if (!config.verbose) return;

    if (panorama.empty()) {
        safePrint("DEBUG: Cannot save empty panorama for " + label + " at frame " + std::to_string(frameIdx), true);
        return;
    }

    try {
        // Speichere Debug-Bild
        std::string debugPath = outputFolder + "/debug_" + label + "_" + std::to_string(frameIdx) + ".JPG";
        std::vector<int> jpegParams = {cv::IMWRITE_JPEG_QUALITY, config.jpegQuality};
        cv::imwrite(debugPath, panorama, jpegParams);

        // Log-Ausgabe
        safePrint("DEBUG: Saved " + label + " at frame " + std::to_string(frameIdx) +
                ", size: " + std::to_string(panorama.cols) + "x" + std::to_string(panorama.rows), true);
    }
    catch (const std::exception& e) {
        safePrint("DEBUG ERROR: Failed to save debug image: " + std::string(e.what()), true);
    }
}

// Hilfsfunktion zum Speichern von Zwischenergebnissen
void saveIntermediatePanorama(
    const cv::Mat& panorama,
    const std::string& outputFolder,
    int frameIdx,
    const StitchConfig& config
) {
    if (panorama.empty()) {
        safePrint("Warning: Cannot save empty intermediate panorama for frame " + std::to_string(frameIdx), true);
        return;
    }

    try {
        std::string intermediatePath = outputFolder + "/intermediate_panorama_" + std::to_string(frameIdx) + ".JPG";
        std::vector<int> jpegParams = {cv::IMWRITE_JPEG_QUALITY, config.jpegQuality};

        // Verwende temporäre Datei, um Probleme bei Abstürzen zu vermeiden
        std::string tempPath = intermediatePath + ".tmp";
        cv::imwrite(tempPath, panorama, jpegParams);

        // Prüfe, ob die temporäre Datei erstellt wurde
        if (!fs_util::exists(tempPath)) {
            throw std::runtime_error("Failed to create temporary intermediate file");
        }

        // Ersetze die alte Datei mit der neuen Version
        if (fs_util::exists(intermediatePath)) {
            remove(intermediatePath.c_str());
        }
        rename(tempPath.c_str(), intermediatePath.c_str());

        safePrint("Saved intermediate result at frame " + std::to_string(frameIdx) +
                 " (" + std::to_string(panorama.cols) + "x" + std::to_string(panorama.rows) + ")", true);
    }
    catch (const std::exception& e) {
        safePrint("Error saving intermediate panorama: " + std::string(e.what()), true);
    }
}

/**
 * Erstellt eine verbesserte Version des Panoramas mit Kontrast- und Schärfeoptimierungen.
 *
 * @param inputPath Pfad zum Eingabe-Panorama
 * @param outputPath Pfad für das verbesserte Panorama
 * @param config Konfigurationseinstellungen
 * @return true bei Erfolg, false bei Fehler
 */
bool createEnhancedPanorama(const std::string& inputPath, const std::string& outputPath, const StitchConfig& config) {
    try {
        safePrint("Creating enhanced version of panorama...", true);

        // Load the source image
        cv::Mat panorama = cv::imread(inputPath);
        if (panorama.empty()) {
            throw std::runtime_error("Failed to load source panorama from: " + inputPath);
        }

        safePrint("Loaded source panorama: " + std::to_string(panorama.cols) + "x" +
                 std::to_string(panorama.rows), true);

        // Enhance the panorama with the existing function
        cv::Mat enhanced = enhancePanorama(panorama, outputPath, config);

        // Check if the file exists
        if (!fs_util::exists(outputPath)) {
            // Try alternative formats if the original format failed
            std::string baseExt = fs_util::extension(outputPath);
            std::vector<std::string> alternativeExts = {".png", ".tiff", ".tif", ".JPG"};

            bool saved = false;
            for (const auto& ext : alternativeExts) {
                if (ext == baseExt) continue; // Skip the already attempted format

                std::string altPath = outputPath.substr(0, outputPath.find_last_of('.')) + ext;
                std::vector<int> compressionParams;

                if (ext == ".png") {
                    compressionParams = {cv::IMWRITE_PNG_COMPRESSION, 1}; // Fast compression
                } else if (ext == ".tiff" || ext == ".tif") {
                    compressionParams = {cv::IMWRITE_TIFF_COMPRESSION, 1}; // LZW compression
                } else {
                    compressionParams = {cv::IMWRITE_JPEG_QUALITY, config.jpegQuality};
                }

                if (cv::imwrite(altPath, enhanced, compressionParams)) {
                    safePrint("Enhanced panorama saved with alternative format: " + ext, true);
                    saved = true;
                    break;
                }
            }

            if (!saved) {
                // Try saving a smaller version
                cv::Mat resizedEnhanced;
                double scale = 0.25;
                cv::resize(enhanced, resizedEnhanced, cv::Size(), scale, scale, cv::INTER_AREA);

                std::string resizedPath = outputPath.substr(0, outputPath.find_last_of('.')) + ".JPG";
                safePrint("Attempting to save resized enhanced panorama", true);

                if (cv::imwrite(resizedPath, resizedEnhanced)) {
                    safePrint("Saved resized enhanced panorama as fallback", true);
                } else {
                    throw std::runtime_error("Enhanced panorama could not be saved with any format");
                }
            }
        } else {
            struct stat fileInfo;
            stat(outputPath.c_str(), &fileInfo);
            std::uintmax_t fileSize = fileInfo.st_size;
            safePrint("Enhanced panorama created successfully: " +
                     std::to_string(fileSize / (1024 * 1024)) + " MB", true);
        }

        return true;
    }
    catch (const std::exception& e) {
        safePrint("Error creating enhanced panorama: " + std::string(e.what()), true);
        return false;
    }
}

void savePanoramaVersions(
    const cv::Mat& panorama,
    const std::string& outputFolder,
    const StitchConfig& config,
    const std::string& serialNumber = "",
    const std::string& subprojectType = ""
) {
    if (panorama.empty()) {
        safePrint("ERROR: Cannot save empty panorama", true);
        return;
    }

    try {
        // Check if panorama is too large for JPEG
        bool isTooBigForJpeg = panorama.cols > 65000 || panorama.rows > 65000;
        safePrint("Panorama dimensions: " + std::to_string(panorama.cols) + "x" +
                 std::to_string(panorama.rows), true);

        if (isTooBigForJpeg) {
            safePrint("WARNING: Panorama exceeds JPEG size limits, will use alternative formats", true);
        }

        // Create a list of formats to try in order of preference
        std::vector<std::pair<std::string, std::vector<int>>> formatOptions;

        if (isTooBigForJpeg) {
            // Large image options
            formatOptions.push_back({".tiff", {cv::IMWRITE_TIFF_COMPRESSION, 1}}); // TIFF with LZW compression
            formatOptions.push_back({".png", {cv::IMWRITE_PNG_COMPRESSION, 1}}); // PNG with minimal compression for speed
            formatOptions.push_back({".tif", {}}); // Basic TIFF, no compression
        } else {
            // Normal sized image options
            formatOptions.push_back({".JPG", {cv::IMWRITE_JPEG_QUALITY, config.jpegQuality}});
            formatOptions.push_back({".png", {cv::IMWRITE_PNG_COMPRESSION, 6}});
        }

        // Base filenames to save
        std::string filePrefix = "tire_unwrapped";

        // Wenn Seriennummer und Subprojekttyp angegeben sind, verwende sie für eindeutige Dateinamen
        if (!serialNumber.empty() && !subprojectType.empty()) {
            // Erstelle einen sicheren Dateinamen aus der Seriennummer (ersetze ungültige Zeichen)
            std::string safeSerial = serialNumber;
            for (char& c : safeSerial) {
                if (!std::isalnum(c)) {
                    c = '_';
                }
            }

            // Erstelle einen Zeitstempel
            auto now = std::chrono::system_clock::now();
            auto now_time_t = std::chrono::system_clock::to_time_t(now);
            std::tm now_tm = *std::localtime(&now_time_t);
            char timestamp[20];
            std::strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", &now_tm);

            // Erstelle eindeutige Dateinamen
            filePrefix = "tire_" + safeSerial + "_" + subprojectType + "_" + timestamp;
            safePrint("Using unique filename prefix: " + filePrefix, true);
        }

        std::vector<std::string> baseFileNames = {
            filePrefix + "_full",
            filePrefix + "_complete",
            filePrefix + "_COMPLETED"
        };

        // Try to save using each format until successful
        bool saveSuccessful = false;
        std::string successFormat;

        for (const auto& format : formatOptions) {
            std::string extension = format.first;
            std::vector<int> compressionParams = format.second;

            try {
                safePrint("Attempting to save with format: " + extension, true);

                // Try to save just one test file first to verify format support
                std::string testPath = outputFolder + "/format_test" + extension;
                bool testSuccess = cv::imwrite(testPath, panorama(cv::Rect(0, 0, std::min(1000, panorama.cols),
                                                                         std::min(1000, panorama.rows))),
                                             compressionParams);

                if (!testSuccess) {
                    safePrint("Format " + extension + " is not supported by this OpenCV build, trying next format", true);
                    continue;
                }

                // Remove test file
                if (fs_util::exists(testPath)) {
                    remove(testPath.c_str());
                }

                // Now try to save all the required files
                bool allFilesSaved = true;

                for (const auto& baseFileName : baseFileNames) {
                    std::string fullPath = outputFolder + "/" + baseFileName + extension;
                    std::string tempPath = fullPath + ".tmp";

                    safePrint("Saving: " + fullPath, true);

                    if (!cv::imwrite(tempPath, panorama, compressionParams)) {
                        safePrint("Failed to write file: " + tempPath, true);
                        allFilesSaved = false;
                        break;
                    }

                    // Verify file was created
                    if (!fs_util::exists(tempPath)) {
                        safePrint("Failed to create file: " + tempPath, true);
                        allFilesSaved = false;
                        break;
                    }

                    struct stat fileInfo;
                    stat(tempPath.c_str(), &fileInfo);
                    std::uintmax_t fileSize = fileInfo.st_size;
                    if (fileSize < 1024) { // less than 1 KB is suspicious
                        safePrint("Warning: Generated file is suspiciously small: " +
                                std::to_string(fileSize) + " bytes", true);
                    }

                    // Safely replace target file
                    if (fs_util::exists(fullPath)) {
                        remove(fullPath.c_str());
                    }
                    rename(tempPath.c_str(), fullPath.c_str());

                    safePrint("Saved: " + baseFileName + extension + " (" +
                             std::to_string(fileSize / (1024*1024)) + " MB)", true);
                }

                if (allFilesSaved) {
                    saveSuccessful = true;
                    successFormat = extension;
                    break;
                }
            }
            catch (const std::exception& e) {
                safePrint("Error saving with format " + extension + ": " + e.what(), true);
            }
        }

        if (saveSuccessful) {
            safePrint("Successfully saved all panorama files using format: " + successFormat, true);

            // Create the enhanced version too if needed
            std::string basePath = outputFolder + "/" + baseFileNames[0] + successFormat;
            std::string enhancedPath = outputFolder + "/" + filePrefix + "_enhanced" + successFormat;

            bool enhancementSuccess = createEnhancedPanorama(basePath, enhancedPath, config);

            if (!enhancementSuccess) {
                safePrint("Warning: Enhanced panorama creation failed, using regular version", true);
                // Copy file manually
                std::ifstream src(basePath, std::ios::binary);
                std::ofstream dst(enhancedPath, std::ios::binary);
                dst << src.rdbuf();
            }
        } else {
            throw std::runtime_error("Failed to save panorama with any available format");
        }
    }
    catch (const std::exception& e) {
        safePrint("ERROR saving panorama versions: " + std::string(e.what()), true);

        // Fall back to section saving as a last resort
        try {
            // Try to save sections as a fallback (already implemented in the existing code)
            safePrint("Falling back to section-based saving", true);

            // Save the panorama in sections
            const int SECTION_WIDTH = 10000;
            int numSections = (panorama.cols + SECTION_WIDTH - 1) / SECTION_WIDTH;

            for (int i = 0; i < numSections; i++) {
                int startX = i * SECTION_WIDTH;
                int width = std::min(SECTION_WIDTH, panorama.cols - startX);

                cv::Mat section = panorama(cv::Rect(startX, 0, width, panorama.rows));

                // Try multiple formats for sections too
                bool sectionSaved = false;
                for (const std::string& ext : {".png", ".tiff", ".JPG"}) {
                    std::string sectionPath = outputFolder + "/tire_unwrapped_section_" + std::to_string(i) + ext;
                    if (cv::imwrite(sectionPath, section)) {
                        sectionSaved = true;
                        safePrint("Saved section " + std::to_string(i+1) + "/" + std::to_string(numSections) +
                                 " as " + ext, true);
                        break;
                    }
                }

                if (!sectionSaved) {
                    // Try with smaller section as last resort
                    cv::Mat smallerSection;
                    cv::resize(section, smallerSection, cv::Size(), 0.5, 0.5, cv::INTER_AREA);
                    if (cv::imwrite(outputFolder + "/tire_unwrapped_section_" + std::to_string(i) + "_small.JPG", smallerSection)) {
                        safePrint("Saved reduced-size section " + std::to_string(i+1) + "/" + std::to_string(numSections), true);
                    } else {
                        safePrint("Failed to save section " + std::to_string(i+1) + "/" + std::to_string(numSections), true);
                    }
                }
            }

            safePrint("Panorama saved in " + std::to_string(numSections) + " sections", true);

            // Also save a downscaled complete version
            try {
                double scale = 1;
                cv::Mat resizedPanorama;
                cv::resize(panorama, resizedPanorama, cv::Size(), scale, scale, cv::INTER_AREA);

                // Create a base filename for the panorama
                std::string baseFileName = "tire_unwrapped";

                // If serial number and subproject type are provided, use them for the filename
                if (!serialNumber.empty() && !subprojectType.empty()) {
                    // Create a safe filename from the serial number
                    std::string safeSerial = serialNumber;
                    for (char& c : safeSerial) {
                        if (!std::isalnum(c)) {
                            c = '_';
                        }
                    }

                    // Create a timestamp
                    auto now = std::chrono::system_clock::now();
                    auto now_time_t = std::chrono::system_clock::to_time_t(now);
                    std::tm now_tm = *std::localtime(&now_time_t);
                    char timestamp[20];
                    std::strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", &now_tm);

                    baseFileName = "tire_" + safeSerial + "_" + subprojectType + "_" + timestamp;
                }

                for (const std::string& ext : {".JPG", ".png", ".tiff"}) {
                    if (cv::imwrite(outputFolder + "/" + baseFileName + "_full" + ext, resizedPanorama)) {
                        safePrint("Saved scaled panorama (" + std::to_string(resizedPanorama.cols) + "x" +
                                 std::to_string(resizedPanorama.rows) + ") as " + ext, true);
                        break;
                    }
                }
            }
            catch (const std::exception& e2) {
                safePrint("Failed to save scaled panorama: " + std::string(e2.what()), true);
            }
        }
        catch (const std::exception& e2) {
            safePrint("CRITICAL: All panorama saving attempts failed: " + std::string(e2.what()), true);
        }
    }
}

cv::Mat stitchTireSurfaceWithOpticalFlow(
    const std::string& inputFolder,
    const std::string& outputFolder,
    const std::string& filePattern,
    bool useBlending,
    int startIdx,
    int maxFrames,
    const StitchConfig& config,
    const std::string& serialNumber,
    const std::string& subprojectType
) {
    auto startTime = std::chrono::high_resolution_clock::now();

    // Add variables for tile management
    int tileCount = 0;
    int framesInCurrentTile = 0;
    const int FRAMES_PER_TILE = 100;
    std::vector<std::string> tilePaths; // To store paths to all generated tiles

    // Create output folder if it doesn't exist
    if (!fs_util::exists(outputFolder)) {
        fs_util::create_directories(outputFolder);
    }

    // Collect all image files matching the pattern
    std::vector<Path> allImagePaths;

    // Use dirent.h to list directory contents
    DIR* dir = opendir(inputFolder.c_str());
    if (dir != nullptr) {
        struct dirent* entry;
        while ((entry = readdir(dir)) != nullptr) {
            std::string filename = entry->d_name;

            // Skip . and .. directories
            if (filename == "." || filename == "..") {
                continue;
            }

            // Check if it's a JPG file matching our pattern
            if (filename.find("IMG_") != std::string::npos &&
                filename.find(".JPG") != std::string::npos) {
                allImagePaths.push_back(Path(inputFolder + "/" + filename));
            }
        }
        closedir(dir);
    }

    if (allImagePaths.empty()) {
        throw std::runtime_error("No images found matching pattern " + filePattern + " in " + inputFolder);
    }

    // Sort paths naturally
    std::sort(allImagePaths.begin(), allImagePaths.end(), naturalSortComparator);

    // Apply start index and maximum frame count if specified
    if (startIdx > 0 && startIdx < allImagePaths.size()) {
        allImagePaths.erase(allImagePaths.begin(), allImagePaths.begin() + startIdx);
    }

    if (maxFrames > 0 && maxFrames < allImagePaths.size()) {
        allImagePaths.resize(maxFrames);
    }

    safePrint("Found " + std::to_string(allImagePaths.size()) + " images to process", true);

    // Need at least 2 images
    if (allImagePaths.size() < 2) {
        throw std::runtime_error("Need at least 2 images to create a panorama");
    }

    // Create an ImageLoader with appropriate thread count
    int loaderThreads = std::max(1, static_cast<int>(std::thread::hardware_concurrency() / 2));
    ImageLoader imageLoader(loaderThreads, config);

    // Cache for loaded images
    std::map<Path, cv::Mat> imageCache;
    std::mutex imageCacheMutex;

    // Preload function
    auto preloadImage = [&](const Path& path) {
        imageLoader.enqueue(path, [&, path](cv::Mat img) {
            if (!img.empty()) {
                std::lock_guard<std::mutex> lock(imageCacheMutex);
                imageCache[path] = img;
            }
        });
    };

    // Image retrieval function (from cache or synchronous loading)
    auto getImage = [&](const Path& path) -> cv::Mat {
        {
            std::lock_guard<std::mutex> lock(imageCacheMutex);
            auto it = imageCache.find(path);
            if (it != imageCache.end()) {
                cv::Mat img = it->second.clone();
                imageCache.erase(it);  // Remove from cache to free memory
                return img;
            }
        }

        // Not in cache, load synchronously
        cv::Mat img = cv::imread(path.string());
        if (!img.empty() && config.resizeScale != 1.0) {
            img = resizeImageForProcessing(img, config.resizeScale);
        }
        return img;
    };

    // Start preloading process for the first images
    const int preloadBatchSize = std::min(20, static_cast<int>(allImagePaths.size()));
    for (int i = 0; i < preloadBatchSize; i++) {
        preloadImage(allImagePaths[i]);
    }

    // Load the first two images
    cv::Mat firstImg = getImage(allImagePaths[0]);
    cv::Mat secondImg = getImage(allImagePaths[1]);

    if (firstImg.empty() || secondImg.empty()) {
        throw std::runtime_error("Could not read initial images");
    }

    // Ensure images are in BGR format
    if (firstImg.channels() == 1) {
        cv::cvtColor(firstImg, firstImg, cv::COLOR_GRAY2BGR);
    }
    if (secondImg.channels() == 1) {
        cv::cvtColor(secondImg, secondImg, cv::COLOR_GRAY2BGR);
    }

    // Calculate initial movement and determine strip width with new method
    double correlationValue = 0.0;
    double initialMovement = measureMovementWithOpticalFlow(firstImg, secondImg, config, &correlationValue);

    // Debug-Ausgabe des genauen Anfangswerts
    safePrint("Raw initial movement: " + std::to_string(initialMovement), true);

    int stripWidthForFirstStrip;

    if (config.resolutionMode == "4K") {
        // Apply user's specified logic for 4K mode
        if (config.stripWidth > 0) {
            stripWidthForFirstStrip = config.stripWidth;
            double dynamicInitialStripWidth = initialMovement + initialMovement / 10.0; // For logging
            safePrint("Using strip width from config for first strip: " + std::to_string(stripWidthForFirstStrip) +
                      " (dynamic calculation would be: " + std::to_string(static_cast<int>(std::round(dynamicInitialStripWidth))) + 
                      " based on movement: " + std::to_string(initialMovement) + ")", config.verbose);
        } else {
            // Fallback to dynamic calculation if config.stripWidth is not positive (e.g. CLI set it to 0 or negative for 4K)
            safePrint("Warning: config.stripWidth is not positive (" + std::to_string(config.stripWidth) + "). Calculating dynamically for first strip.", config.verbose);
            stripWidthForFirstStrip = static_cast<int>(std::round(initialMovement + initialMovement / 10.0));
            if (stripWidthForFirstStrip <= 0) { // Robust fallback
                stripWidthForFirstStrip = 20; // 4K specific fallback
                safePrint("Dynamic calculation for first strip resulted in <=0. Using fallback: " + std::to_string(stripWidthForFirstStrip), config.verbose);
            }
        }
    } else { // For 8K or other modes (non-4K) - Keep existing logic
        safePrint("8K/Other Mode: Calculating strip width for first strip dynamically.", config.verbose);
        double calculatedDynamicStripWidthFloat = initialMovement + initialMovement / 10.0;  // 10% margin
        stripWidthForFirstStrip = static_cast<int>(std::round(calculatedDynamicStripWidthFloat));
        if (stripWidthForFirstStrip <= 0) {
            stripWidthForFirstStrip = 50; // Fallback for 8K/Other
            safePrint("8K/Other Mode: Dynamic calculation for first strip resulted in <=0. Using fallback: " + std::to_string(stripWidthForFirstStrip), config.verbose);
        }
    }

    // Unified logging for the chosen stripWidthForFirstStrip
    safePrint("Initial movement: " + std::to_string(initialMovement) +
              ", Chosen strip width for first strip: " + std::to_string(stripWidthForFirstStrip) +
              ", Initial correlation: " + std::to_string(correlationValue), true);

    // Extract first strip with the chosen width
    cv::Mat firstStrip = getCentralStrip(firstImg, stripWidthForFirstStrip);
    cv::Mat panorama = firstStrip.clone();

    // Debug-Ausgabe des ersten Strips
    debugPanorama(firstStrip, outputFolder, "first_strip", 0, config);

    // Keep the last strip and the last image for the next iteration
    cv::Mat lastStrip = firstStrip.clone();
    cv::Mat lastImg = firstImg.clone();
    int lastStripWidth = stripWidthForFirstStrip; // IMPORTANT: This is used in error fallback

    // Initialize frames in current tile
    framesInCurrentTile = 1; // Count the first frame

    // Running average movement for stability
    // Use a ring buffer for the last N values
    const int MOVING_AVERAGE_SIZE = 5;
    std::deque<double> recentMovements;
    recentMovements.push_back(initialMovement);

    // Statistics file for tracking values
    std::string statsFile = outputFolder + "/stitching_stats.csv";
    std::ofstream stats(statsFile);
    stats << "frame,tile,movement,correlation,strip_width,overlap,flow_used,processing_time_ms" << std::endl;
    stats << "0," << tileCount << "," << initialMovement << "," << correlationValue << ","
          << stripWidthForFirstStrip << ",0,false,0" << std::endl;

    // Configuration for JPEG compression when saving
    std::vector<int> jpegParams = {cv::IMWRITE_JPEG_QUALITY, config.jpegQuality};

    // Process each subsequent image
    for (size_t i = 1; i < allImagePaths.size(); i++) {
        auto frameStartTime = std::chrono::high_resolution_clock::now();

        int frameIdx = i;
        safePrint("Processing frame " + std::to_string(frameIdx) + "/" +
                 std::to_string(allImagePaths.size() - 1) + " (Tile " + std::to_string(tileCount) + ")", true);

        // Start preloading for the next images
        int nextPreloadIdx = i + preloadBatchSize;
        if (nextPreloadIdx < allImagePaths.size()) {
            preloadImage(allImagePaths[nextPreloadIdx]);
        }

        // Load current image
        cv::Mat currentImg = getImage(allImagePaths[i]);

        if (currentImg.empty()) {
            safePrint("Warning: Skipping unreadable image: " + allImagePaths[i].string(), true);
            continue;
        }

        try {
            // Ensure the image is in BGR format
            if (currentImg.channels() == 1) {
                cv::cvtColor(currentImg, currentImg, cv::COLOR_GRAY2BGR);
            }

            // Measure movement between frames with combined method
            double correlation = 0.0;
            double movement = measureMovementWithOpticalFlow(lastImg, currentImg, config, &correlation);

            // DEBUG: Anzeige des genauen Wertes ohne Rundung
            safePrint("Raw calculated movement: " + std::to_string(movement), true);

            // Check if we used optical flow
            bool usedOpticalFlow = (correlation < 0.95);

            safePrint("Correlation value: " + std::to_string(correlation), true);

            // Update the moving average
            recentMovements.push_back(movement);
            if (recentMovements.size() > MOVING_AVERAGE_SIZE) {
                recentMovements.pop_front();
            }

            // Calculate average movement with double precision
            double averageMovement = std::accumulate(recentMovements.begin(), recentMovements.end(), 0.0) /
                                    static_cast<double>(recentMovements.size());

            // Use average for more stable results, except for significant deviations
            double finalMovement = movement;
            if (!usedOpticalFlow && std::abs(movement - averageMovement) < averageMovement * 0.2) {
                // If correlation-based movement is close to average, use the average
                finalMovement = averageMovement;
                safePrint("Using average movement for stability: " + std::to_string(finalMovement), config.verbose);
            } else {
                safePrint("Using direct measured movement: " + std::to_string(finalMovement) +
                         (usedOpticalFlow ? " (from optical flow)" : " (from correlation)"), config.verbose);
            }

            // Initialize stripWidth and overlap
            int stripWidth;
            int overlap;
            double stripWidthFloat; // For logging
            double overlapFloat;    // For logging

            if (config.resolutionMode == "4K") {
                if (config.stripWidth > 0) {
                    stripWidth = config.stripWidth; // Use explicitly configured stripWidth
                    stripWidthFloat = static_cast<double>(stripWidth);
                    safePrint("4K mode: Using configured stripWidth: " + std::to_string(stripWidth), config.verbose);
                } else {
                    // config.stripWidth is not set, calculate dynamically
                    // Using a 15% margin on movement for strip width.
                    stripWidthFloat = finalMovement + finalMovement * 0.15; // 15% margin
                    stripWidth = static_cast<int>(std::round(stripWidthFloat));
                    safePrint("4K mode: Dynamically calculated stripWidth: " + std::to_string(stripWidth) +
                              " (based on movement: " + std::to_string(finalMovement) + ")", config.verbose);
                }

                // Overlap calculation for 4K:
                // Adaptive based on movement, with a 10% factor.
                overlapFloat = finalMovement * 0.10; 
                overlap = static_cast<int>(std::round(overlapFloat));
                safePrint("4K mode: Initial movement-based overlap: " + std::to_string(overlap) + 
                          " (from movement " + std::to_string(finalMovement) + ")", config.verbose);

                // Apply caps and minimums for overlap in 4K mode
                if (stripWidth > 0) {
                    // Cap overlap at 50% of stripWidth
                    if (overlap > stripWidth / 2) {
                        overlap = stripWidth / 2;
                        safePrint("4K mode: Overlap capped to 50% of stripWidth: " + std::to_string(overlap), config.verbose);
                    }
                    // Ensure overlap is at least 1, if stripWidth allows for any overlap
                    // If stripWidth is 1, overlap must be 0.
                    if (overlap < 1 && stripWidth > 1) { 
                        overlap = 1;
                        safePrint("4K mode: Overlap floored to 1.", config.verbose);
                    } else if (stripWidth <= 1) { 
                        overlap = 0; // Overlap must be 0 if stripWidth is 1 or less
                    }
                } else { // If stripWidth is 0 or less (after initial calculation, before validation)
                    overlap = 0;
                }
                // Ensure overlap is not negative (should be caught by stripWidth <= 0 check already if stripWidth is also negative)
                if (overlap < 0) {
                    overlap = 0;
                }
                
                safePrint("4K mode: Final calculated stripWidth: " + std::to_string(stripWidth) +
                          ", Final calculated overlap: " + std::to_string(overlap), config.verbose);

            } else { // Original logic for 8K (or non-4K modes)
                stripWidthFloat = finalMovement + finalMovement / 10.0;  // 10% margin
                stripWidth = static_cast<int>(std::round(stripWidthFloat));

                overlapFloat = finalMovement / 10.0;  // 10% of movement as overlap
                overlap = static_cast<int>(std::round(overlapFloat));

                // Limit high overlaps to a reasonable value
                if (overlap > stripWidth / 2) {
                    overlap = stripWidth / 2;
                }
                safePrint("8K/Other mode: Calculated stripWidth: " + std::to_string(stripWidth) + ", Calculated overlap: " + std::to_string(overlap), config.verbose);
            }

            // Common validations for stripWidth and overlap, AFTER mode-specific calculations
            // Ensure stripWidth is at least 1
            if (stripWidth < 1) {
                safePrint("Warning: Calculated stripWidth is less than 1 (" + std::to_string(stripWidth) + "). Setting to 1.", true, config);
                stripWidth = 1;
                // If stripWidth was adjusted to 1, overlap might need re-adjustment if it was based on a larger stripWidth or became invalid.
                if (overlap >= stripWidth) {
                    overlap = 0; // Max possible overlap for stripWidth = 1 is 0.
                    safePrint("Overlap adjusted to 0 due to stripWidth being set to 1.", true, config);
                }
            }
            // Ensure overlap is less than stripWidth and non-negative.
            if (overlap >= stripWidth) {
                safePrint("Warning: Calculated overlap (" + std::to_string(overlap) + ") is >= stripWidth (" + std::to_string(stripWidth) + "). Adjusting overlap.", true, config);
                overlap = std::max(0, stripWidth - 1); 
            }
            if (overlap < 0) { // Should be redundant if previous logic is correct, but as a safeguard.
                 safePrint("Warning: Calculated overlap is negative (" + std::to_string(overlap) + "). Setting to 0.", true, config);
                 overlap = 0;
            }

            // Extract the strip from the current image
            cv::Mat currentStrip = getCentralStrip(currentImg, stripWidth);

            // Debug output of the strip
            debugPanorama(currentStrip, outputFolder, "strip", frameIdx, config);

            // Debug output
            safePrint("Frame " + std::to_string(frameIdx) +
                     ": Movement=" + std::to_string(finalMovement) +
                     ", Correlation=" + std::to_string(correlation) +
                     ", Strip width float=" + std::to_string(stripWidthFloat) +
                     ", Strip width=" + std::to_string(stripWidth) +
                     ", Overlap float=" + std::to_string(overlapFloat) +
                     ", Overlap=" + std::to_string(overlap) +
                     ", Optical flow=" + (usedOpticalFlow ? "true" : "false"), config.verbose);

            // Blend or add the new strip to the panorama
            if (useBlending) {
                try {
                    // Create a blended panorama
                    cv::Mat blended = blendImages(panorama, currentStrip, overlap, config);
                    panorama = blended;
                }
                catch (const std::exception& e) {
                    safePrint("Warning: Blending failed: " + std::string(e.what()), true);
                    safePrint("Falling back to simple concatenation", true);

                    // Simple concatenation
                    cv::Mat nonOverlap = currentStrip(cv::Rect(overlap, 0, currentStrip.cols - overlap, currentStrip.rows));
                    cv::Mat newPanorama(panorama.rows, panorama.cols + nonOverlap.cols, panorama.type());
                    panorama.copyTo(newPanorama(cv::Rect(0, 0, panorama.cols, panorama.rows)));
                    nonOverlap.copyTo(newPanorama(cv::Rect(panorama.cols, 0, nonOverlap.cols, nonOverlap.rows)));
                    panorama = newPanorama;
                }
            }
            else {
                // Simple concatenation with overlap
                cv::Mat nonOverlap = currentStrip(cv::Rect(overlap, 0, currentStrip.cols - overlap, currentStrip.rows));
                cv::Mat newPanorama(panorama.rows, panorama.cols + nonOverlap.cols, panorama.type());
                panorama.copyTo(newPanorama(cv::Rect(0, 0, panorama.cols, panorama.rows)));
                nonOverlap.copyTo(newPanorama(cv::Rect(panorama.cols, 0, nonOverlap.cols, nonOverlap.rows)));
                panorama = newPanorama;
            }

            // Debug-Ausgabe des Panorama-Status nach der Addition
            debugPanorama(panorama, outputFolder, "panorama", frameIdx, config);

            // Update for next iteration
            lastStrip = currentStrip.clone();
            lastImg = currentImg.clone();
            lastStripWidth = stripWidth;

            // Calculate processing time for this frame
            auto frameEndTime = std::chrono::high_resolution_clock::now();
            auto frameProcessingTime = std::chrono::duration_cast<std::chrono::milliseconds>(
                frameEndTime - frameStartTime).count();

            // Log statistics
            stats << frameIdx << "," << tileCount << "," << finalMovement << "," << correlation << ","
                  << stripWidth << "," << overlap << "," << (usedOpticalFlow ? "true" : "false")
                  << "," << frameProcessingTime << std::endl;

            // Increment frames in current tile
            framesInCurrentTile++;

            // Save intermediate results periodically
            if (frameIdx % 10 == 0 || frameIdx == allImagePaths.size() - 1) {
                saveIntermediatePanorama(panorama, outputFolder, frameIdx, config);
            }

            // Check if we need to start a new tile (after FRAMES_PER_TILE frames)
            if (framesInCurrentTile >= FRAMES_PER_TILE && i < allImagePaths.size() - 1) {
                // Save the current tile
                std::string tilePath = outputFolder + "/tile_" + std::to_string(tileCount) + ".JPG";
                cv::imwrite(tilePath, panorama, jpegParams);
                tilePaths.push_back(tilePath);

                safePrint("Saved tile " + std::to_string(tileCount) + " with " +
                         std::to_string(framesInCurrentTile) + " frames", true);

                // Reset for next tile
                tileCount++;
                framesInCurrentTile = 0;

                // Reset the panorama to start a new tile
                // Use the current frame's strip as the starting point for the new tile
                panorama = currentStrip.clone();

                // Reset the frames counter for the new tile
                framesInCurrentTile = 1; // Count the current frame
            }
        }
        catch (const std::exception& e) {
            safePrint("Warning: Error processing frame " + std::to_string(frameIdx) + ": " + e.what(), true);
            // Use last movement as fallback
            int fallbackOverlap = std::max(10, static_cast<int>(lastStripWidth * 0.2));

            // Get the strip with the last known good width
            cv::Mat currentStrip = getCentralStrip(currentImg, lastStripWidth);

            // Simple concatenation
            cv::Mat nonOverlap = currentStrip(cv::Rect(fallbackOverlap, 0,
                                                       currentStrip.cols - fallbackOverlap,
                                                       currentStrip.rows));
            cv::Mat newPanorama(panorama.rows, panorama.cols + nonOverlap.cols, panorama.type());
            panorama.copyTo(newPanorama(cv::Rect(0, 0, panorama.cols, panorama.rows)));
            nonOverlap.copyTo(newPanorama(cv::Rect(panorama.cols, 0, nonOverlap.cols, nonOverlap.rows)));
            panorama = newPanorama;

            // Update the variables, but without referring to stripWidth
            lastStrip = currentStrip.clone();
            lastImg = currentImg.clone();
            // lastStripWidth remains unchanged

            // Increment frames in current tile even when there's an error
            framesInCurrentTile++;

            // Check if we need to start a new tile after this frame
            if (framesInCurrentTile >= FRAMES_PER_TILE && i < allImagePaths.size() - 1) {
                // Save the current tile
                std::string tilePath = outputFolder + "/tile_" + std::to_string(tileCount) + ".JPG";
                cv::imwrite(tilePath, panorama, jpegParams);
                tilePaths.push_back(tilePath);

                safePrint("Saved tile " + std::to_string(tileCount) + " with " +
                         std::to_string(framesInCurrentTile) + " frames", true);

                // Reset for next tile
                tileCount++;
                framesInCurrentTile = 0;

                // Reset the panorama to start a new tile
                panorama = currentStrip.clone();
                framesInCurrentTile = 1; // Count the current frame
            }
        }
    }

    // Save the final tile if not already saved
    if (framesInCurrentTile > 0) {
        std::string tilePath = outputFolder + "/tile_" + std::to_string(tileCount) + ".JPG";
        cv::imwrite(tilePath, panorama, jpegParams);
        tilePaths.push_back(tilePath);

        safePrint("Saved final tile " + std::to_string(tileCount) + " with " +
                 std::to_string(framesInCurrentTile) + " frames", true);
    }

    // Save metadata about the tiles for later linking
    std::string tilesInfoPath = outputFolder + "/tiles_info.json";
    std::ofstream tilesInfo(tilesInfoPath);
    tilesInfo << "{\n";
    tilesInfo << "  \"total_tiles\": " << tilePaths.size() << ",\n";
    tilesInfo << "  \"frames_per_tile\": " << FRAMES_PER_TILE << ",\n";
    tilesInfo << "  \"tiles\": [\n";
    for (size_t i = 0; i < tilePaths.size(); i++) {
        tilesInfo << "    {\n";
        tilesInfo << "      \"index\": " << i << ",\n";
        tilesInfo << "      \"path\": \"" << fs_util::filename(tilePaths[i]) << "\",\n";
        tilesInfo << "      \"start_frame\": " << (i * FRAMES_PER_TILE) << ",\n";
        int frameCount = (i < tilePaths.size() - 1) ? FRAMES_PER_TILE : framesInCurrentTile;
        tilesInfo << "      \"end_frame\": " << ((i * FRAMES_PER_TILE) + frameCount - 1) << "\n";
        tilesInfo << "    }";
        if (i < tilePaths.size() - 1) {
            tilesInfo << ",";
        }
        tilesInfo << "\n";
    }
    tilesInfo << "  ]\n";
    tilesInfo << "}\n";
    tilesInfo.close();

    // VERBESSERTE KOMBINATION DER KACHELN
    cv::Mat completePanorama;
    try {
        auto combineStartTime = std::chrono::high_resolution_clock::now();
        safePrint("Combining " + std::to_string(tilePaths.size()) + " panorama tiles with blending...", true);

        // Namen für die Ausgabedateien definieren mit eindeutigen Namen basierend auf Seriennummer und Subprojekttyp
        std::string filePrefix = "tire_unwrapped";

        // Wenn Seriennummer und Subprojekttyp angegeben sind, verwende sie für eindeutige Dateinamen
        if (!serialNumber.empty() && !subprojectType.empty()) {
            // Erstelle einen sicheren Dateinamen aus der Seriennummer (ersetze ungültige Zeichen)
            std::string safeSerial = serialNumber;
            for (char& c : safeSerial) {
                if (!std::isalnum(c)) {
                    c = '_';
                }
            }

            // Erstelle einen Zeitstempel
            auto now = std::chrono::system_clock::now();
            auto now_time_t = std::chrono::system_clock::to_time_t(now);
            std::tm now_tm = *std::localtime(&now_time_t);
            char timestamp[20];
            std::strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", &now_tm);

            // Erstelle eindeutige Dateinamen
            filePrefix = "tire_" + safeSerial + "_" + subprojectType + "_" + timestamp;
            safePrint("Using unique filename prefix: " + filePrefix, true);
        }

        std::string workingPath = outputFolder + "/" + filePrefix + "_working.JPG";
        std::string finalCompletedPath = outputFolder + "/" + filePrefix + "_COMPLETED.JPG";
        std::string finalFullPath = outputFolder + "/" + filePrefix + "_full.JPG";
        std::string finalCompletePath = outputFolder + "/" + filePrefix + "_complete.JPG";

        if (tilePaths.empty()) {
            throw std::runtime_error("No tiles found to combine");
        }

        // Lade die erste Kachel
        cv::Mat firstTile = cv::imread(tilePaths[0]);
        if (firstTile.empty()) {
            throw std::runtime_error("Failed to load first tile: " + tilePaths[0]);
        }

        // Optimale Kachelüberlappung basierend auf der Bildgröße berechnen
        int tileOverlap = std::max(20, firstTile.cols / 200);  // Mindestens 20px, skaliert mit Bildgröße
        if (firstTile.cols >= 7000) {  // 8K-Bilder (ungefähr 7680px breit)
            tileOverlap = std::max(tileOverlap, 40);  // Bei sehr großen Bildern: Mindestens 40px
        }

        safePrint("Using tile overlap of " + std::to_string(tileOverlap) + " pixels for " +
                std::to_string(firstTile.cols) + "px wide tiles", true);

        // Wähle die beste Strategie basierend auf der Größe
        const size_t IN_MEMORY_THRESHOLD = 1000 * 1024 * 1024; // 1000 MB

        // Schätze den Speicherbedarf für das gesamte Panorama
        size_t totalWidthEstimate = 0;
        for (const auto& tilePath : tilePaths) {
            cv::Mat tileHeader = cv::imread(tilePath);
            if (!tileHeader.empty()) {
                totalWidthEstimate += tileHeader.cols;
                tileHeader.release();
            }
        }

        // Berücksichtige die Überlappung
        if (tilePaths.size() > 1) {
            totalWidthEstimate -= (tilePaths.size() - 1) * tileOverlap;
        }

        size_t bytesPerPixel = firstTile.channels() * sizeof(uchar);
        size_t totalBytesEstimate = totalWidthEstimate * firstTile.rows * bytesPerPixel;

        safePrint("Estimated final size: " + std::to_string(totalWidthEstimate) + "x" +
                std::to_string(firstTile.rows) + " (" +
                std::to_string(totalBytesEstimate / (1024*1024)) + " MB)", true);

        // Initialisiere mit der ersten Kachel
        completePanorama = firstTile.clone();

        // Lade und kombiniere jede folgende Kachel mit Blending
        for (size_t i = 1; i < tilePaths.size(); i++) {
            safePrint("Processing tile " + std::to_string(i) + "/" +
                    std::to_string(tilePaths.size() - 1) + " with blending", true);

            try {
                // Lade die nächste Kachel
                cv::Mat nextTile = cv::imread(tilePaths[i]);
                if (nextTile.empty()) {
                    throw std::runtime_error("Failed to load tile: " + tilePaths[i]);
                }

                // Falls Höhen nicht übereinstimmen, passe an
                if (nextTile.rows != completePanorama.rows) {
                    cv::resize(nextTile, nextTile, cv::Size(nextTile.cols, completePanorama.rows));
                }

                // Anwenden des Blendings zwischen Kacheln
                cv::Mat blendedPanorama;
                try {
                    // Verwende dieselbe blendImages-Funktion, die für Frames verwendet wird
                    blendedPanorama = blendImages(completePanorama, nextTile, tileOverlap, config);

                    // Ersetze das aktuelle Panorama durch das kombinierte
                    completePanorama = blendedPanorama;

                    safePrint("Successfully blended tile " + std::to_string(i) +
                            " (Panorama size: " + std::to_string(completePanorama.cols) + "x" +
                            std::to_string(completePanorama.rows) + ")", true);
                }
                catch (const std::exception& e) {
                    safePrint("Error during tile blending: " + std::string(e.what()), true);
                    safePrint("Falling back to simple concatenation", true);

                    // Bei Fehlern: Füge Kachel ohne Blending an
                    int overlapWidth = tileOverlap;
                    cv::Mat nonOverlap = nextTile(cv::Rect(overlapWidth, 0,
                                                nextTile.cols - overlapWidth, nextTile.rows));

                    int newWidth = completePanorama.cols + nonOverlap.cols;
                    cv::Mat newPanorama(completePanorama.rows, newWidth, completePanorama.type());
                    completePanorama.copyTo(newPanorama(cv::Rect(0, 0, completePanorama.cols, completePanorama.rows)));
                    nonOverlap.copyTo(newPanorama(cv::Rect(completePanorama.cols, 0, nonOverlap.cols, nonOverlap.rows)));

                    completePanorama = newPanorama;
                }

                // Speicher freigeben
                nextTile.release();

                // Speichere Checkpoints
                if (i % 5 == 0 && i > 0) {
                    safePrint("Saving checkpoint at tile " + std::to_string(i), true);
                    cv::imwrite(workingPath, completePanorama, jpegParams);
                }
            }
            catch (const std::exception& e) {
                safePrint("Error processing tile " + std::to_string(i) + ": " + e.what(), true);
                safePrint("Continuing with the next tile", true);
                // Fortsetzen anstatt den Prozess vollständig fehlschlagen zu lassen
            }
        }

        // Speichere alle Versionen des fertigen Panoramas
        safePrint("Saving final panorama with size: " + std::to_string(completePanorama.cols) + "x" +
                std::to_string(completePanorama.rows), true);
        savePanoramaVersions(completePanorama, outputFolder, config, serialNumber, subprojectType);

        auto combineEndTime = std::chrono::high_resolution_clock::now();
        auto combineDuration = std::chrono::duration_cast<std::chrono::seconds>(
            combineEndTime - combineStartTime).count();

        safePrint("Complete panorama created successfully with dimensions " +
                std::to_string(completePanorama.cols) + "x" +
                std::to_string(completePanorama.rows), true);
        safePrint("Tile combination time: " + std::to_string(combineDuration) + " seconds", true);

        // Löschen der temporären Arbeitsdatei
        if (fs_util::exists(workingPath)) {
            remove(workingPath.c_str());
            safePrint("Cleaned up temporary working file", true);
        }
    }
    catch (const std::exception& e) {
        safePrint("ERROR: Failed to combine panorama tiles: " + std::string(e.what()), true);

        // Robustere Notfalllösung - versuche die Kacheln manuell zu kombinieren
        try {
            safePrint("Attempting emergency panorama construction...", true);

            // Lade zunächst alle Kacheln, die gelesen werden können
            std::vector<cv::Mat> emergencyTiles;
            int totalWidth = 0;
            int maxHeight = 0;

            for (const auto& tilePath : tilePaths) {
                cv::Mat tile = cv::imread(tilePath);
                if (!tile.empty()) {
                    totalWidth += tile.cols;
                    maxHeight = std::max(maxHeight, tile.rows);
                    emergencyTiles.push_back(tile);
                }
            }

            if (emergencyTiles.empty()) {
                throw std::runtime_error("No valid tiles could be loaded");
            }

            // Erstelle ein leeres Panorama
            completePanorama = cv::Mat(maxHeight, totalWidth, emergencyTiles[0].type(), cv::Scalar(0, 0, 0));

            // Kopiere die Kacheln ins Panorama
            int currentX = 0;
            for (const auto& tile : emergencyTiles) {
                // Gleiche die Höhe an, falls nötig
                cv::Mat resizedTile;
                if (tile.rows != maxHeight) {
                    cv::resize(tile, resizedTile, cv::Size(tile.cols, maxHeight));
                } else {
                    resizedTile = tile;
                }

                // Kopiere in Zielpanorama
                cv::Rect roi(currentX, 0, resizedTile.cols, resizedTile.rows);
                resizedTile.copyTo(completePanorama(roi));
                currentX += resizedTile.cols;
            }

            safePrint("Emergency panorama created successfully with dimensions " +
                    std::to_string(completePanorama.cols) + "x" +
                    std::to_string(completePanorama.rows), true);

            // Speichere alle Versionen
            savePanoramaVersions(completePanorama, outputFolder, config, serialNumber, subprojectType);
        }
        catch (const std::exception& e2) {
            safePrint("CRITICAL: Emergency panorama creation failed: " + std::string(e2.what()), true);

            // Absolute Notfalllösung - verwende zumindest eine Kachel
            try {
                if (!tilePaths.empty()) {
                    // Verwende die erste Kachel als absolutes Minimum
                    cv::Mat singleTile = cv::imread(tilePaths[0]);
                    if (!singleTile.empty()) {
                        completePanorama = singleTile;
                        safePrint("CRITICAL: Using only first tile as fallback panorama", true);
                        savePanoramaVersions(completePanorama, outputFolder, config, serialNumber, subprojectType);
                    }
                }
            }
            catch (...) {
                safePrint("FATAL ERROR: All panorama creation attempts failed", true);
            }
        }
    }

    // Stelle sicher, dass panorama das kombinierte Ergebnis enthält
    if (!completePanorama.empty()) {
        panorama = completePanorama.clone();
    }

    // Calculate total processing time
    auto endTime = std::chrono::high_resolution_clock::now();
    auto totalDuration = std::chrono::duration_cast<std::chrono::seconds>(
        endTime - startTime).count();

    safePrint("Created " + std::to_string(tilePaths.size()) + " panorama tiles", true);
    safePrint("Tiles metadata saved to " + tilesInfoPath, true);
    safePrint("Total processing time: " + std::to_string(totalDuration) + " seconds", true);

    // Finale Überprüfung, dass das Panorama nicht leer ist
    if (panorama.empty()) {
        safePrint("WARNING: Return panorama is empty, this should not happen!", true);

        // Letzte Chance: Versuche, ein Panorama zu finden
        cv::Mat lastChance;

        // Suche nach Dateien, die mit "tire_" beginnen und mit "_full.JPG" enden
        // Use dirent.h to list directory contents
        DIR* dir = opendir(outputFolder.c_str());
        if (dir != nullptr) {
            struct dirent* entry;
            while ((entry = readdir(dir)) != nullptr) {
                std::string filename = entry->d_name;

                // Skip . and .. directories
                if (filename == "." || filename == "..") {
                    continue;
                }

                if (filename.find("_full.JPG") != std::string::npos) {
                    std::string fullPath = outputFolder + "/" + filename;
                    lastChance = cv::imread(fullPath);
                    if (!lastChance.empty()) {
                        safePrint("Loaded saved panorama from: " + fullPath, true);
                        break;
                    }
                }
            }
            closedir(dir);
        }
        if (!lastChance.empty()) {
            panorama = lastChance;
            safePrint("Loaded saved panorama as last resort", true);
        }
    }

    return panorama;
}

// Neue Implementierung des hierarchischen Stitchings
cv::Mat stitchTireSurfaceHierarchical(
    const std::string& inputFolder,
    const std::string& outputFolder,
    const std::string& filePattern,
    bool useBlending,
    int startIdx,
    int maxFrames,
    const StitchConfig& config,
    const std::string& serialNumber,
    const std::string& subprojectType
) {
    auto startTime = std::chrono::high_resolution_clock::now();

    // Create output folder if it doesn't exist
    if (!fs_util::exists(outputFolder)) {
        fs_util::create_directories(outputFolder);
    }

    // Collect all image files matching the pattern
    std::vector<Path> allImagePaths;

    // Use dirent.h to list directory contents
    DIR* dir = opendir(inputFolder.c_str());
    if (dir != nullptr) {
        struct dirent* entry;
        while ((entry = readdir(dir)) != nullptr) {
            std::string filename = entry->d_name;

            // Skip . and .. directories
            if (filename == "." || filename == "..") {
                continue;
            }

            // Check if it's a JPG file matching our pattern
            if (filename.find("IMG_") != std::string::npos &&
                filename.find(".JPG") != std::string::npos) {
                allImagePaths.push_back(Path(inputFolder + "/" + filename));
            }
        }
        closedir(dir);
    }

    if (allImagePaths.empty()) {
        throw std::runtime_error("No images found matching pattern " + filePattern + " in " + inputFolder);
    }

    // Sort paths naturally
    std::sort(allImagePaths.begin(), allImagePaths.end(), naturalSortComparator);

    // Apply start index and maximum frame count if specified
    if (startIdx > 0 && startIdx < allImagePaths.size()) {
        allImagePaths.erase(allImagePaths.begin(), allImagePaths.begin() + startIdx);
    }

    if (maxFrames > 0 && maxFrames < allImagePaths.size()) {
        allImagePaths.resize(maxFrames);
    }

    safePrint("Found " + std::to_string(allImagePaths.size()) + " images to process in hierarchical mode", true);

    // Need at least 2 images
    if (allImagePaths.size() < 2) {
        throw std::runtime_error("Need at least 2 images to create a panorama");
    }

    // Create an ImageLoader with appropriate thread count
    int loaderThreads = std::max(1, static_cast<int>(std::thread::hardware_concurrency() / 2));
    ImageLoader imageLoader(loaderThreads, config);

    // Cache for loaded images and processed intermediate results
    std::map<Path, cv::Mat> imageCache;
    std::map<std::pair<int, int>, cv::Mat> stitchedCache; // Cache für Zwischenergebnisse [start,end] -> Mat
    std::mutex cacheMutex;

    // Statistics file for tracking values
    std::string statsFile = outputFolder + "/hierarchical_stitching_stats.csv";
    std::ofstream stats(statsFile);
    stats << "level,group,start_idx,end_idx,num_images,processing_time_ms" << std::endl;

    // Preload function
    auto preloadImage = [&](const Path& path) {
        imageLoader.enqueue(path, [&, path](cv::Mat img) {
            if (!img.empty()) {
                std::lock_guard<std::mutex> lock(cacheMutex);
                imageCache[path] = img;
            }
        });
    };

    // Image retrieval function (from cache or synchronous loading)
    auto getImage = [&](const Path& path) -> cv::Mat {
        {
            std::lock_guard<std::mutex> lock(cacheMutex);
            auto it = imageCache.find(path);
            if (it != imageCache.end()) {
                cv::Mat img = it->second.clone();
                return img;
            }
        }

        // Not in cache, load synchronously
        cv::Mat img = cv::imread(path.string());
        if (!img.empty() && config.resizeScale != 1.0) {
            img = resizeImageForProcessing(img, config.resizeScale);
        }
        return img;
    };

    // Start preloading process for all images
    const int preloadBatchSize = std::min(50, static_cast<int>(allImagePaths.size()));
    for (int i = 0; i < preloadBatchSize; i++) {
        preloadImage(allImagePaths[i]);
    }

    // Recursive function to stitch a range of images hierarchically
    std::function<cv::Mat(int, int, int)> stitchRange = [&](int start, int end, int level) -> cv::Mat {
        auto groupStartTime = std::chrono::high_resolution_clock::now();
        int groupSize = end - start + 1;

        safePrint("Processing level " + std::to_string(level) + " group [" +
                 std::to_string(start) + "," + std::to_string(end) + "] with " +
                 std::to_string(groupSize) + " images", config.verbose);

        // Check if this range is already in cache
        {
            std::lock_guard<std::mutex> lock(cacheMutex);
            auto cacheKey = std::make_pair(start, end);
            auto it = stitchedCache.find(cacheKey);
            if (it != stitchedCache.end()) {
                return it->second.clone();
            }
        }

        // Base case: single image
        if (start == end) {
            cv::Mat img = getImage(allImagePaths[start]);

            if (img.empty()) {
                throw std::runtime_error("Could not read image at index " + std::to_string(start));
            }

            // Ensure image is in BGR format
            if (img.channels() == 1) {
                cv::cvtColor(img, img, cv::COLOR_GRAY2BGR);
            }

            auto groupEndTime = std::chrono::high_resolution_clock::now();
            auto processingTime = std::chrono::duration_cast<std::chrono::milliseconds>(
                groupEndTime - groupStartTime).count();

            stats << level << "," << start << "," << start << "," << end << ",1,"
                  << processingTime << std::endl;

            return img;
        }

        // Base case: two adjacent images - direct stitch
        if (start + 1 == end) {
            cv::Mat img1 = getImage(allImagePaths[start]);
            cv::Mat img2 = getImage(allImagePaths[end]);

            if (img1.empty() || img2.empty()) {
                throw std::runtime_error("Could not read images at indices " +
                                        std::to_string(start) + " and " + std::to_string(end));
            }

            // Ensure images are in BGR format
            if (img1.channels() == 1) {
                cv::cvtColor(img1, img1, cv::COLOR_GRAY2BGR);
            }
            if (img2.channels() == 1) {
                cv::cvtColor(img2, img2, cv::COLOR_GRAY2BGR);
            }

            // Stitch the pair of images
            cv::Mat result = stitchImagePair(img1, img2, useBlending, config);

            // Cache the result
            {
                std::lock_guard<std::mutex> lock(cacheMutex);
                stitchedCache[std::make_pair(start, end)] = result.clone();
            }

            auto groupEndTime = std::chrono::high_resolution_clock::now();
            auto processingTime = std::chrono::duration_cast<std::chrono::milliseconds>(
                groupEndTime - groupStartTime).count();

            stats << level << "," << start << "," << start << "," << end << ",2,"
                  << processingTime << std::endl;

            // Save intermediate results for larger groups
            if (groupSize > 10) {
                std::string intermediatePath = outputFolder + "/level" + std::to_string(level) +
                                              "_group_" + std::to_string(start) + "_" +
                                              std::to_string(end) + ".JPG";
                std::vector<int> jpegParams = {cv::IMWRITE_JPEG_QUALITY, config.jpegQuality};
                cv::imwrite(intermediatePath, result, jpegParams);
            }

            return result;
        }

        // Recursive case: split into roughly equal parts for logarithmic division
        int mid = start + (end - start) / 2;

        // Recursively stitch the left and right parts
        cv::Mat leftPart = stitchRange(start, mid, level + 1);
        cv::Mat rightPart = stitchRange(mid + 1, end, level + 1);

        // Stitch the two parts together
        cv::Mat result = stitchImagePair(leftPart, rightPart, useBlending, config);

        // Cache the result
        {
            std::lock_guard<std::mutex> lock(cacheMutex);
            stitchedCache[std::make_pair(start, end)] = result.clone();
        }

        auto groupEndTime = std::chrono::high_resolution_clock::now();
        auto processingTime = std::chrono::duration_cast<std::chrono::milliseconds>(
            groupEndTime - groupStartTime).count();

        stats << level << "," << start << "," << start << "," << end << "," << groupSize << ","
              << processingTime << std::endl;

        // Save intermediate results for larger groups
        if (groupSize > 10 || level <= 2) {
            std::string intermediatePath = outputFolder + "/level" + std::to_string(level) +
                                          "_group_" + std::to_string(start) + "_" +
                                          std::to_string(end) + ".JPG";
            std::vector<int> jpegParams = {cv::IMWRITE_JPEG_QUALITY, config.jpegQuality};
            cv::imwrite(intermediatePath, result, jpegParams);
        }

        // Free memory for intermediate images that are no longer needed
        if (groupSize > 10) {
            std::lock_guard<std::mutex> lock(cacheMutex);
            stitchedCache.erase(std::make_pair(start, mid));
            stitchedCache.erase(std::make_pair(mid + 1, end));
        }

        return result;
    };

    // Start the hierarchical stitching from the full range
    cv::Mat panorama = stitchRange(0, allImagePaths.size() - 1, 0);

    // Speichere alle Versionen des fertigen Panoramas
    savePanoramaVersions(panorama, outputFolder, config, serialNumber, subprojectType);

    // Calculate total processing time
    auto endTime = std::chrono::high_resolution_clock::now();
    auto totalDuration = std::chrono::duration_cast<std::chrono::seconds>(
        endTime - startTime).count();

    safePrint("Final panorama shape: " + std::to_string(panorama.cols) + "x" +
             std::to_string(panorama.rows), true);
    safePrint("Hierarchical stitching statistics saved to " + statsFile, true);
    safePrint("Total processing time: " + std::to_string(totalDuration) + " seconds", true);

    return panorama;
}

// Helper function to stitch a pair of images
cv::Mat stitchImagePair(
    const cv::Mat& img1,
    const cv::Mat& img2,
    bool useBlending,
    const StitchConfig& config
) {
    // Calculate movement between frames with the existing method
    double correlation = 0.0;
    double movement = measureMovementWithOpticalFlow(img1, img2, config, &correlation);

    // Debug-Ausgabe bei Bedarf
    if (config.verbose) {
        safePrint("Pair stitching - calculated movement: " + std::to_string(movement) +
                 ", correlation: " + std::to_string(correlation), true);
    }

    // Berechne Streifenbreite mit Sicherheitsrand (10%)
    double stripWidthFloat = movement + movement / 10.0;
    int stripWidth = static_cast<int>(std::round(stripWidthFloat));

    // Extrahiere zentralen Streifen aus dem zweiten Bild
    cv::Mat strip2 = getCentralStrip(img2, stripWidth);

    // Berechne optimale Überlappungsgröße basierend auf Auflösung und Korrelation
    int minOverlap = 5;  // Mindestüberlappung
    int scaledOverlap = std::max(minOverlap, img1.cols / 1000);  // Skaliert mit der Bildbreite

    // Bei 8K-Bildern (~ 7680 Pixel Breite) wird die Überlappung entsprechend erhöht
    if (img1.cols >= 7000) {
        scaledOverlap = std::max(scaledOverlap, 20);  // Mindestens 20 Pixel bei großen Bildern
    }

    // Bei hoher Korrelation können wir eine kleinere Überlappung verwenden
    int overlap = (correlation > 0.95) ?
                  std::max(minOverlap, scaledOverlap / 2) :
                  scaledOverlap;

    // Blend or add the new strip to the panorama
    cv::Mat panorama;
    if (useBlending) {
        try {
            // Create a blended panorama
            panorama = blendImages(img1, strip2, overlap, config);
        }
        catch (const std::exception& e) {
            safePrint("Warning: Blending failed: " + std::string(e.what()), config.verbose);
            safePrint("Falling back to simple concatenation", config.verbose);

            // Simple concatenation
            cv::Mat nonOverlap = strip2(cv::Rect(overlap, 0, strip2.cols - overlap, strip2.rows));
            cv::Mat newPanorama(img1.rows, img1.cols + nonOverlap.cols, img1.type());
            img1.copyTo(newPanorama(cv::Rect(0, 0, img1.cols, img1.rows)));
            nonOverlap.copyTo(newPanorama(cv::Rect(img1.cols, 0, nonOverlap.cols, nonOverlap.rows)));
            panorama = newPanorama;
        }
    }
    else {
        // Simple concatenation with overlap
        cv::Mat nonOverlap = strip2(cv::Rect(overlap, 0, strip2.cols - overlap, strip2.rows));
        cv::Mat newPanorama(img1.rows, img1.cols + nonOverlap.cols, img1.type());
        img1.copyTo(newPanorama(cv::Rect(0, 0, img1.cols, img1.rows)));
        nonOverlap.copyTo(newPanorama(cv::Rect(img1.cols, 0, nonOverlap.cols, nonOverlap.rows)));
        panorama = newPanorama;
    }

    return panorama;
}