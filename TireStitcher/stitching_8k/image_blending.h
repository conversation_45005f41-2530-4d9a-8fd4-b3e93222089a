#ifndef STITCH_TIRE_IMAGE_BLENDING_H
#define STITCH_TIRE_IMAGE_BLENDING_H

#include "config.h"

#include <opencv2/opencv.hpp>

// Blend two images with an overlapping region
cv::Mat blendImages(const cv::Mat& img1, const cv::Mat& img2, int overlapWidth, const StitchConfig& config);

// Create an enhanced version of a panorama with contrast and sharpness improvements
cv::Mat enhancePanorama(const cv::Mat& panorama, const std::string& outputPath, const StitchConfig& config);

// Get or create a blending mask for the specified width
cv::Mat getBlendingMask(int width, int height);

#endif // STITCH_TIRE_IMAGE_BLENDING_H