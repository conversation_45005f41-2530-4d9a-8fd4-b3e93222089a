#ifndef STITCH_TIRE_CONFIG_H
#define STITCH_TIRE_CONFIG_H

#include <string>
#include <mutex>
#include <iostream>

// Mutex for thread-safe output
extern std::mutex coutMutex;

// Thread-safe print function
template<typename T>
void safePrint(const T& message, bool force = false, const struct StitchConfig& config = {});

struct StitchConfig {
    bool verbose;
    int jpegQuality;
    int templateMatchPrecision;
    double resizeScale;
    bool useHierarchicalStitching; // Neue Option für hierarchisches Stitching
    bool keepTemporaryFiles;
    std::string resolutionMode; // Added for 4K/8K differentiation
    int stripWidth; // Added for configurable strip width

    StitchConfig() :
        verbose(false),
        jpegQuality(98),
        templateMatchPrecision(2),
        resizeScale(1.0),
        useHierarchicalStitching(true),
        keepTemporaryFiles(false),
        resolutionMode("8K"), // Default to 8K
        stripWidth(0) // Default to 0, indicating not set by CLI for 4K special handling
    {}
};

// Thread-safe print function implementation
// Modified to not output to console
template<typename T>
void safePrint(const T& message, bool force, const StitchConfig& config) {
    // Disabled console output as per requirements
    // Only critical errors will be logged through the Python error handler
    return;
}

#endif // STITCH_TIRE_CONFIG_H