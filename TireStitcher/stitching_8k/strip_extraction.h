#ifndef STITCH_TIRE_STRIP_EXTRACTION_H
#define STITCH_TIRE_STRIP_EXTRACTION_H

#include <opencv2/opencv.hpp>
#include "config.h"

// Extract the central strip from an image
cv::Mat getCentralStrip(const cv::Mat& image, int stripWidth);

// Measure movement between two image strips with correlation
double measureMovement(const cv::Mat& img1, const cv::Mat& img2, const StitchConfig& config);

// Calculate optical flow between two image strips
cv::Mat calculateOpticalFlow(const cv::Mat& img1, const cv::Mat& img2);

// Extract movement information from optical flow
double extractMovementFromFlow(const cv::Mat& flow, bool horizontal = true);

// Forward declaration only - definition is in movement_detection.h
double measureMovementWithOpticalFlow(const cv::Mat& img1, const cv::Mat& img2, 
                                     const StitchConfig& config,
                                     double* outCorrelation);

// Blend two images with overlapping regions
cv::Mat blendImages(const cv::Mat& img1, const cv::Mat& img2, int overlapWidth, const StitchConfig& config);

#endif // STITCH_TIRE_STRIP_EXTRACTION_H