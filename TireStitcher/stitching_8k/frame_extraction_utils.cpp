#include "frame_extraction_utils.h"

#ifdef _WIN32
// Implementation of directory functions for Windows
DIR* opendir(const char* name) {
    DIR* dir = new DIR;
    std::string pattern = std::string(name) + "\\*";
    dir->handle = FindFirstFileA(pattern.c_str(), &dir->data);
    dir->first = true;
    if (dir->handle == INVALID_HANDLE_VALUE) {
        delete dir;
        return nullptr;
    }
    return dir;
}

dirent* readdir(DIR* dir) {
    if (dir->first) {
        dir->first = false;
    } else {
        if (!FindNextFileA(dir->handle, &dir->data)) {
            return nullptr;
        }
    }
    strncpy(dir->entry.d_name, dir->data.cFileName, 260); // Use 260 directly instead of MAX_PATH
    return &dir->entry;
}

int closedir(DIR* dir) {
    if (dir) {
        FindClose(dir->handle);
        delete dir;
        return 0;
    }
    return -1;
}
#endif
