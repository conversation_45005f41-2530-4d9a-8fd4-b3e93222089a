#ifndef STITCH_TIRE_FRAME_EXTRACTION_UTILS_H
#define STITCH_TIRE_FRAME_EXTRACTION_UTILS_H

#include <opencv2/opencv.hpp>
#include <string>
#include <chrono>
#include <fstream>
#include <map>
#include <direct.h>  // For _mkdir on Windows
#include <sys/stat.h> // For stat
#include "fs_util.h"

#ifdef _WIN32
    #include <windows.h>

    // Windows-specific directory handling
    struct dirent {
        char d_name[260]; // Use 260 directly instead of MAX_PATH
    };

    typedef struct {
        WIN32_FIND_DATAA data;
        HANDLE handle;
        dirent entry;
        bool first;
    } DIR;

    DIR* opendir(const char* name);
    dirent* readdir(DIR* dir);
    int closedir(DIR* dir);
#else
    #include <dirent.h> // For directory operations on Linux/Unix
#endif

// Configuration options for frame extraction
struct ExtractOptions {
    bool autoRotate = true;    // Auto-detect rotation
    bool forceRotate = false;  // Force rotate frames (only when autoRotate is false)
    int sampleRate = 1;        // Extract every n-th frame
    bool forceOpenCV = false;  // Force use of OpenCV
    int maxFrames = 0;         // Maximum frames to extract (0 = all)
};

// Timer for measuring performance
class PerformanceTimer {
private:
    std::chrono::high_resolution_clock::time_point startTime;
    std::string operationName;

public:
    PerformanceTimer(const std::string& name) : operationName(name) {
        startTime = std::chrono::high_resolution_clock::now();
        std::cout << "Starting " << operationName << "..." << std::endl;
    }

    ~PerformanceTimer() {
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            endTime - startTime).count();
        std::cout << operationName << " completed in " << duration << "ms" << std::endl;
    }

    long getElapsedMilliseconds() {
        auto endTime = std::chrono::high_resolution_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            endTime - startTime).count();
    }

    long getElapsedSeconds() {
        auto endTime = std::chrono::high_resolution_clock::now();
        return std::chrono::duration_cast<std::chrono::seconds>(
            endTime - startTime).count();
    }
};

// Count the number of frames extracted in a directory
inline int countExtractedFrames(const std::string& outputFolder) {
    int frameCount = 0;

    DIR* dir = opendir(outputFolder.c_str());
    if (dir != nullptr) {
        struct dirent* entry;
        while ((entry = readdir(dir)) != nullptr) {
            std::string filename = entry->d_name;
            if (filename.find("IMG_") != std::string::npos &&
                filename.find(".JPG") != std::string::npos) {
                frameCount++;
            }
        }
        closedir(dir);
    }

    return frameCount;
}

// Save extraction info to a file
inline void saveExtractionInfo(
    const std::string& outputFolder,
    const std::string& videoPath,
    const std::string& method,
    int frameCount,
    const ExtractOptions& options,
    int processingTime,
    const std::map<std::string, std::string>& additionalInfo = {})
{
    std::string infoPath = outputFolder + "/extract_info.txt";
    std::ofstream infoFile(infoPath);
    if (infoFile.is_open()) {
        infoFile << "Video: " << videoPath << std::endl;
        infoFile << "Method: " << method << std::endl;
        infoFile << "Frames extracted: " << frameCount << std::endl;
        infoFile << "Sample rate: 1/" << options.sampleRate << std::endl;
        infoFile << "Auto-rotation: " << (options.autoRotate ? "Enabled" : "Disabled") << std::endl;
        infoFile << "Processing time: " << processingTime << " seconds" << std::endl;

        // Add any additional information
        for (const auto& [key, value] : additionalInfo) {
            infoFile << key << ": " << value << std::endl;
        }

        infoFile.close();
    }
}

#endif // STITCH_TIRE_FRAME_EXTRACTION_UTILS_H