#ifndef MOVEMENT_DETECTION_H
#define MOVEMENT_DETECTION_H

#include <opencv2/opencv.hpp>
#include <deque>
#include <mutex>
#include "config.h"

// Hauptfunktion zur Bewegungserkennung
double measureMovementWithOpticalFlow(
    const cv::Mat& img1, 
    const cv::Mat& img2, 
    const StitchConfig& config,
    double* outCorrelation = nullptr
);

// Hilfsfunktionen
double calculateCorrelation(
    const cv::Mat& img1, 
    const cv::Mat& img2, 
    const StitchConfig& config
);

void runTemplateMatching(
    const cv::Mat& img1,
    const cv::Mat& img2,
    std::vector<double>& allMovements,
    std::vector<double>& allCorrelations,
    std::vector<double>& allConfidences,
    const StitchConfig& config
);

// Optimiert alle berechneten Bewegungen global, um kumulative Fehler zu reduzieren
std::vector<double> optimizeMovementsGlobally(
    const std::vector<double>& rawMovements, 
    const StitchConfig& config
);

// Deklaration der in strip_extraction.cpp definierten Funktionen
double extractMovementFromFlow(const cv::Mat& flow, bool horizontal, bool verbose);
cv::Mat calculateOpticalFlow(const cv::Mat& prevImg, const cv::Mat& nextImg);

#endif // MOVEMENT_DETECTION_H