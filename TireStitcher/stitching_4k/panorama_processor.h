#ifndef PANORAMA_PROCESSOR_H
#define PANORAMA_PROCESSOR_H

#include "config.h"
#include "image_loader.h"  // Importieren Sie image_loader.h, um die vorhandenen Funktionen zu verwenden
#include <opencv2/opencv.hpp>
#include <string>
#include <vector>
#include <functional>
#include <map>

// fs namespace is already defined in image_loader.h

// Bestehende Funktionssignatur für sequentielles Stitching
cv::Mat stitchTireSurfaceWithOpticalFlow(
    const std::string& inputFolder,
    const std::string& outputFolder,
    const std::string& filePattern,
    bool useBlending,
    int startIdx,
    int maxFrames,
    const StitchConfig& config,
    const std::string& serialNumber = "",
    const std::string& subprojectType = ""
);

// Hilfsfunktion zum periodischen Speichern von Zwischenergebnissen
void saveIntermediatePanorama(
    const cv::Mat& panorama,
    const std::string& outputFolder,
    int frameIdx,
    const StitchConfig& config
);

// Neue Funktion für hierarchisches Stitching
cv::Mat stitchTireSurfaceHierarchical(
    const std::string& inputFolder,
    const std::string& outputFolder,
    const std::string& filePattern,
    bool useBlending,
    int startIdx,
    int maxFrames,
    const StitchConfig& config,
    const std::string& serialNumber = "",
    const std::string& subprojectType = ""
);

// Hilfsfunktion zum Stitchen von zwei Bildern
cv::Mat stitchImagePair(
    const cv::Mat& img1,
    const cv::Mat& img2,
    bool useBlending,
    const StitchConfig& config
);

#endif // PANORAMA_PROCESSOR_H