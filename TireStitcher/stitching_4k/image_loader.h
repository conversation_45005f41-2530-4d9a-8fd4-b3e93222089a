#ifndef STITCH_TIRE_IMAGE_LOADER_H
#define STITCH_TIRE_IMAGE_LOADER_H

#include "config.h"

#include <opencv2/opencv.hpp>
#include <queue>
#include <thread>
#include <mutex>
#include <functional>
#include <condition_variable>
#include <vector>
#include <string>
#include <iostream> // Added for logging

// Define DEBUG_PATH_LOGGING if not already defined by build system
// #define DEBUG_PATH_LOGGING

// Forward declare the fs_util namespace
namespace fs_util {
    bool exists(const std::string& path);
    bool create_directory(const std::string& path);
    bool create_directories(const std::string& path);
    std::string filename(const std::string& path);
    std::string extension(const std::string& path);
}

// Define a simple path class to replace std::filesystem::path
class Path {
private:
    std::string m_path;

public:
    Path() = default;
    Path(const std::string& path) : m_path(path) {
#ifdef DEBUG_PATH_LOGGING
        std::cout << "DEBUG_PATH_LOGGING: Path object created with path: " << m_path << std::endl;
#endif
    }

    std::string string() const { 
#ifdef DEBUG_PATH_LOGGING
        // Avoid logging in string() const if it's called very frequently from other logging statements to prevent recursion.
        // std::cout << "DEBUG_PATH_LOGGING: Path::string() called for: " << m_path << std::endl;
#endif
        return m_path; 
    }
    std::string filename() const { 
#ifdef DEBUG_PATH_LOGGING
        std::cout << "DEBUG_PATH_LOGGING: Path::filename() called for: " << m_path << std::endl;
#endif
        return fs_util::filename(m_path); 
    }
    std::string extension() const { 
#ifdef DEBUG_PATH_LOGGING
        std::cout << "DEBUG_PATH_LOGGING: Path::extension() called for: " << m_path << std::endl;
#endif
        return fs_util::extension(m_path); 
    }

    operator std::string() const { return m_path; }

    // Add operator< for use in std::map
    bool operator<(const Path& other) const {
        return m_path < other.m_path;
    }
};

// Asynchronous image loading with thread pool
class ImageLoader {
private:
    std::queue<std::pair<Path, std::function<void(cv::Mat)>>> taskQueue;
    std::vector<std::thread> workers;
    std::mutex queueMutex;
    std::condition_variable condition;
    bool stop;
    StitchConfig config;

public:
    // Constructor takes number of threads and configuration
    ImageLoader(int numThreads, const StitchConfig& cfg);

    // Destructor ensures clean shutdown
    ~ImageLoader();

    // Enqueue a new image loading task
    void enqueue(const Path& imagePath, std::function<void(cv::Mat)> callback);
};

// Optimized function for image resizing
cv::Mat resizeImageForProcessing(const cv::Mat& image, double scaleFactor);

// Helper function for natural sorting of image filenames
bool naturalSortComparator(const Path& a, const Path& b);

#endif // STITCH_TIRE_IMAGE_LOADER_H