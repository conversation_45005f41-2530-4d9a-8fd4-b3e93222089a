#include "image_blending.h"

#include <map>
#include <mutex>
#include <chrono>

// External references
extern std::map<int, cv::Mat> blendingMaskCache;
extern std::mutex cacheMutex;

cv::Mat blendImages(const cv::Mat& img1, const cv::Mat& img2, int overlapWidth, const StitchConfig& config) {
    int h1 = img1.rows;
    int w1 = img1.cols;
    int h2 = img2.rows;
    int w2 = img2.cols;
    
    // Ensure consistent height
    cv::Mat resizedImg2;
    if (h1 != h2) {
        cv::resize(img2, resizedImg2, cv::Size(w2, h1));
    } else {
        resizedImg2 = img2;
    }
    
    // The output image will have width of both images minus the overlap
    int outputWidth = w1 + w2 - overlapWidth;
    cv::Mat result(h1, outputWidth, img1.type(), cv::Scalar(0, 0, 0));
    
    // Copy the non-overlapping part of the first image
    img1(cv::Rect(0, 0, w1 - overlapWidth, h1)).copyTo(
        result(cv::Rect(0, 0, w1 - overlapWidth, h1)));
    
    // Create or get blending mask from cache
    cv::Mat mask;
    {
        std::lock_guard<std::mutex> lock(cacheMutex);
        if (blendingMaskCache.find(overlapWidth) != blendingMaskCache.end()) {
            mask = blendingMaskCache[overlapWidth];
        } else {
            mask = cv::Mat(h1, overlapWidth, CV_32F);
            
            // Create a linear gradient for the overlap region
            #pragma omp parallel for
            for (int i = 0; i < overlapWidth; i++) {
                float value = 1.0f - (static_cast<float>(i) / overlapWidth);
                mask.col(i).setTo(value);
            }
            
            // Store in cache
            blendingMaskCache[overlapWidth] = mask.clone();
        }
    }
    
    // Extract overlap regions
    cv::Mat overlapRegion1 = img1(cv::Rect(w1 - overlapWidth, 0, overlapWidth, h1));
    cv::Mat overlapRegion2 = resizedImg2(cv::Rect(0, 0, overlapWidth, h1));
    
    // Perform blending
    cv::Mat blendedOverlap(h1, overlapWidth, img1.type());
    
    // Process each channel separately for blending
    std::vector<cv::Mat> channels1, channels2, blendedChannels;
    cv::split(overlapRegion1, channels1);
    cv::split(overlapRegion2, channels2);
    blendedChannels.resize(channels1.size());
    
    // Use OpenMP for parallel channel processing
    #pragma omp parallel for
    for (int c = 0; c < channels1.size(); c++) {
        cv::Mat weightedChannel1, weightedChannel2;
        
        // Extend mask for multiplication with multi-channel images
        cv::Mat channelMask;
        if (channels1.size() > 1) {
            // For multi-channel images
            channelMask = mask.clone();
        } else {
            // For grayscale images
            channelMask = mask;
        }
        
        cv::multiply(channels1[c], channelMask, weightedChannel1, 1.0, CV_32F);
        
        cv::Mat invMask;
        cv::subtract(cv::Scalar(1.0), channelMask, invMask);
        
        cv::multiply(channels2[c], invMask, weightedChannel2, 1.0, CV_32F);
        blendedChannels[c] = weightedChannel1 + weightedChannel2;
    }
    
    cv::merge(blendedChannels, blendedOverlap);
    
    // Convert back to original type
    if (blendedOverlap.type() != img1.type()) {
        blendedOverlap.convertTo(blendedOverlap, img1.type());
    }
    
    // Copy the blended area
    blendedOverlap.copyTo(result(cv::Rect(w1 - overlapWidth, 0, overlapWidth, h1)));
    
    // Copy the non-overlapping part of the second image
    resizedImg2(cv::Rect(overlapWidth, 0, w2 - overlapWidth, h1)).copyTo(
        result(cv::Rect(w1, 0, w2 - overlapWidth, h1)));
    
    return result;
}

cv::Mat getBlendingMask(int width, int height) {
    // Check cache first
    {
        std::lock_guard<std::mutex> lock(cacheMutex);
        if (blendingMaskCache.find(width) != blendingMaskCache.end()) {
            return blendingMaskCache[width].clone();
        }
    }
    
    // Create a new mask
    cv::Mat mask(height, width, CV_32F);
    
    // Create a linear gradient mask
    #pragma omp parallel for
    for (int i = 0; i < width; i++) {
        float value = 1.0f - (static_cast<float>(i) / width);
        mask.col(i).setTo(value);
    }
    
    // Store in cache
    {
        std::lock_guard<std::mutex> lock(cacheMutex);
        blendingMaskCache[width] = mask.clone();
    }
    
    return mask;
}

cv::Mat enhancePanorama(const cv::Mat& panorama, const std::string& outputPath, const StitchConfig& config) {
    safePrint("Creating enhanced panorama...", true);
    auto startTime = std::chrono::high_resolution_clock::now();
    
    cv::Mat enhanced;
    
    // CPU-based enhancement
    cv::cvtColor(panorama, enhanced, cv::COLOR_BGR2YUV);
    
    // Split YUV channels
    std::vector<cv::Mat> channels;
    cv::split(enhanced, channels);
    
    // OpenMP parallelization for channel processing
    #pragma omp parallel sections
    {
        #pragma omp section
        {
            // Apply CLAHE to Y channel
            cv::Ptr<cv::CLAHE> clahe = cv::createCLAHE(2.0, cv::Size(8, 8));
            clahe->apply(channels[0], channels[0]);
        }
    }
    
    // Merge channels back
    cv::merge(channels, enhanced);
    
    // Convert back to BGR
    cv::cvtColor(enhanced, enhanced, cv::COLOR_YUV2BGR);
    
    // Unsharp mask for sharpening
    cv::Mat blurred;
    cv::GaussianBlur(enhanced, blurred, cv::Size(0, 0), 3);
    cv::addWeighted(enhanced, 1.5, blurred, -0.5, 0, enhanced);
    
    // Save the enhanced image
    std::vector<int> jpegParams = {cv::IMWRITE_JPEG_QUALITY, config.jpegQuality};
    cv::imwrite(outputPath, enhanced, jpegParams);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        endTime - startTime).count();
    
    safePrint("Enhanced panorama saved to: " + outputPath, true);
    safePrint("Enhancement time: " + std::to_string(duration) + " ms", true);
    
    return enhanced;
}