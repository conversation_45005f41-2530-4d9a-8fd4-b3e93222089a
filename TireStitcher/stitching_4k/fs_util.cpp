#include "fs_util.h"

namespace fs_util {
    // Check if a directory exists
    bool exists(const std::string& path) {
#ifdef DEBUG_PATH_LOGGING
        std::cout << "DEBUG_PATH_LOGGING: Checking existence of path: " << path << std::endl;
#endif
        struct stat info;
        return stat(path.c_str(), &info) == 0;
    }

    // Create a directory
    bool create_directory(const std::string& path) {
#ifdef DEBUG_PATH_LOGGING
        std::cout << "DEBUG_PATH_LOGGING: Creating directory: " << path << std::endl;
#endif
        #ifdef _WIN32
            return _mkdir(path.c_str()) == 0;
        #else
            return mkdir(path.c_str(), 0755) == 0;
        #endif
    }

    // Create directories recursively
    bool create_directories(const std::string& path) {
#ifdef DEBUG_PATH_LOGGING
        std::cout << "DEBUG_PATH_LOGGING: Creating directories recursively for path: " << path << std::endl;
#endif
        // Simple implementation for Windows paths
        std::string current;
        size_t pos = 0;

        // Handle absolute paths
        if (path.size() > 1 && path[1] == ':') {
            current = path.substr(0, 3); // Include drive letter and :\
            pos = 3;
        }

        while (pos < path.size()) {
            size_t nextPos = path.find_first_of("/\\", pos);
            if (nextPos == std::string::npos) {
                nextPos = path.size();
            }

            if (nextPos > pos) {
                current += path.substr(pos, nextPos - pos);
#ifdef DEBUG_PATH_LOGGING
                std::cout << "DEBUG_PATH_LOGGING: Current path segment: " << current << std::endl;
#endif
                if (!exists(current)) {
#ifdef DEBUG_PATH_LOGGING
                    std::cout << "DEBUG_PATH_LOGGING: Directory does not exist, creating: " << current << std::endl;
#endif
                    if (!create_directory(current)) {
#ifdef DEBUG_PATH_LOGGING
                        std::cerr << "DEBUG_PATH_LOGGING: Failed to create directory: " << current << std::endl;
#endif
                        return false;
                    }
                }
                if (nextPos < path.size()) {
                    current += path[nextPos];
                }
            }
            pos = nextPos + 1;
        }
        return true;
    }

    // Get filename from path
    std::string filename(const std::string& path) {
#ifdef DEBUG_PATH_LOGGING
        std::cout << "DEBUG_PATH_LOGGING: Getting filename from path: " << path << std::endl;
#endif
        size_t pos = path.find_last_of("/\\");
        if (pos != std::string::npos) {
            return path.substr(pos + 1);
        }
        return path;
    }

    // Get file extension
    std::string extension(const std::string& path) {
#ifdef DEBUG_PATH_LOGGING
        std::cout << "DEBUG_PATH_LOGGING: Getting extension from path: " << path << std::endl;
#endif
        size_t pos = path.find_last_of(".");
        if (pos != std::string::npos) {
            return path.substr(pos);
        }
        return "";
    }
}
