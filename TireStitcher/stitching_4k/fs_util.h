#ifndef FS_UTIL_H
#define FS_UTIL_H

#include <string>
#include <direct.h>  // For _mkdir on Windows
#include <sys/stat.h> // For stat
#include <iostream> // Added for logging

// Custom file utilities to replace filesystem
namespace fs_util {
    // Check if a directory exists
    bool exists(const std::string& path);
    
    // Create a directory
    bool create_directory(const std::string& path);
    
    // Create directories recursively
    bool create_directories(const std::string& path);
    
    // Get filename from path
    std::string filename(const std::string& path);
    
    // Get file extension
    std::string extension(const std::string& path);
}

#endif // FS_UTIL_H
