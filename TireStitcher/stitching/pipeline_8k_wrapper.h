#ifndef PIPELINE_8K_WRAPPER_H
#define PIPELINE_8K_WRAPPER_H

#include <opencv2/opencv.hpp>
#include <string>
#include "config.h"

/**
 * Wrapper namespace for 8K pipeline to avoid naming conflicts.
 */
namespace Pipeline8K {
    /**
     * Stitch tire surface using 8K-optimized pipeline.
     */
    cv::Mat stitchTireSurface(
        const std::string& inputFolder,
        const std::string& outputFolder,
        const std::string& filePattern,
        bool useBlending,
        int startIdx,
        int maxFrames,
        const StitchConfig& config,
        const std::string& serialNumber = "",
        const std::string& subprojectType = ""
    );

    /**
     * Enhance panorama using 8K pipeline.
     */
    cv::Mat enhancePanorama(
        const cv::Mat& panorama,
        const std::string& outputPath,
        const StitchConfig& config
    );
}

#endif // PIPELINE_8K_WRAPPER_H
