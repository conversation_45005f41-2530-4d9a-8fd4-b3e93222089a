#include "stitching_factory.h"
#include "pipeline_4k_wrapper.h"
#include "pipeline_8k_wrapper.h"
#include <iostream>
#include <cstring>  // For strncpy

// Include directory handling utilities (same as existing pipelines)
#ifdef _WIN32
    #include <windows.h>

    // Windows-specific directory handling (same as frame_extraction_utils.h)
    struct dirent {
        char d_name[260];
    };

    typedef struct {
        WIN32_FIND_DATAA data;
        HANDLE handle;
        dirent entry;
        bool first;
    } DIR;

    // Forward declarations for directory functions
    DIR* opendir(const char* name);
    dirent* readdir(DIR* dir);
    int closedir(DIR* dir);
#else
    #include <dirent.h>
#endif

cv::Mat StitchingFactory::stitchTireSurface(
    const std::string& inputFolder,
    const std::string& outputFolder,
    const std::string& filePattern,
    bool useBlending,
    int startIdx,
    int maxFrames,
    const StitchConfig& config,
    const std::string& serialNumber,
    const std::string& subprojectType
) {
    // Determine which pipeline to use
    std::string resolutionMode = determineResolutionMode(inputFolder, config);

    std::cout << "Using " << resolutionMode << " stitching pipeline" << std::endl;

    if (resolutionMode == "4K") {
        // Use 4K pipeline
        return Pipeline4K::stitchTireSurface(
            inputFolder, outputFolder, filePattern,
            useBlending, startIdx, maxFrames, config,
            serialNumber, subprojectType
        );
    } else {
        // Use 8K pipeline (default)
        return Pipeline8K::stitchTireSurface(
            inputFolder, outputFolder, filePattern,
            useBlending, startIdx, maxFrames, config,
            serialNumber, subprojectType
        );
    }
}

cv::Mat StitchingFactory::enhancePanorama(
    const cv::Mat& panorama,
    const std::string& outputPath,
    const StitchConfig& config
) {
    // For enhancement, we can use either pipeline - let's use 8K as default
    // since enhancement is less resolution-dependent
    return Pipeline8K::enhancePanorama(panorama, outputPath, config);
}

std::string StitchingFactory::determineResolutionMode(
    const std::string& inputFolder,
    const StitchConfig& config
) {
    // First, check if resolution mode is explicitly set in config
    if (!config.resolutionMode.empty() &&
        (config.resolutionMode == "4K" || config.resolutionMode == "8K")) {
        return config.resolutionMode;
    }

    // If not set, try to auto-detect from first image in folder
    try {
        // Use dirent.h to list directory contents (same as existing pipelines)
        DIR* dir = opendir(inputFolder.c_str());
        if (dir != nullptr) {
            struct dirent* entry;
            while ((entry = readdir(dir)) != nullptr) {
                std::string filename = entry->d_name;

                // Skip . and .. directories
                if (filename == "." || filename == "..") {
                    continue;
                }

                // Check if it matches common image extensions
                if (filename.find(".JPG") != std::string::npos ||
                    filename.find(".jpg") != std::string::npos ||
                    filename.find(".PNG") != std::string::npos ||
                    filename.find(".png") != std::string::npos) {

                    // Load image and check dimensions
                    std::string fullPath = inputFolder + "/" + filename;
                    cv::Mat img = cv::imread(fullPath);
                    if (!img.empty()) {
                        int height = img.rows;
                        // Typical 4K height is around 2160, 8K is around 4320
                        // Use 3000 as threshold
                        if (height >= 2000 && height <= 3000) {
                            std::cout << "Auto-detected 4K resolution from image dimensions: "
                                      << img.cols << "x" << img.rows << std::endl;
                            closedir(dir);
                            return "4K";
                        } else if (height > 3000) {
                            std::cout << "Auto-detected 8K resolution from image dimensions: "
                                      << img.cols << "x" << img.rows << std::endl;
                            closedir(dir);
                            return "8K";
                        }
                    }
                    break;  // Only check first image
                }
            }
            closedir(dir);
        }
    } catch (const std::exception& e) {
        std::cout << "Warning: Could not auto-detect resolution: " << e.what() << std::endl;
    }

    // Default to 8K if detection fails
    std::cout << "Using default 8K resolution mode" << std::endl;
    return "8K";
}

#ifdef _WIN32
// Implementation of directory functions for Windows (same as frame_extraction_utils.cpp)
DIR* opendir(const char* name) {
    DIR* dir = new DIR;
    std::string pattern = std::string(name) + "\\*";
    dir->handle = FindFirstFileA(pattern.c_str(), &dir->data);
    dir->first = true;
    if (dir->handle == INVALID_HANDLE_VALUE) {
        delete dir;
        return nullptr;
    }
    return dir;
}

dirent* readdir(DIR* dir) {
    if (dir->first) {
        dir->first = false;
    } else {
        if (!FindNextFileA(dir->handle, &dir->data)) {
            return nullptr;
        }
    }
    strncpy(dir->entry.d_name, dir->data.cFileName, 260);
    return &dir->entry;
}

int closedir(DIR* dir) {
    if (dir) {
        FindClose(dir->handle);
        delete dir;
        return 0;
    }
    return -1;
}
#endif
