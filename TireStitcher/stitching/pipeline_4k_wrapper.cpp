#include "pipeline_4k_wrapper.h"

// Include 4K pipeline headers
#include "../stitching_4k/panorama_processor.h"
#include "../stitching_4k/config.h"
#include "../stitching_4k/image_blending.h"
#include "../stitching_4k/debug_utils.h"

namespace Pipeline4K {
    cv::Mat stitchTireSurface(
        const std::string& inputFolder,
        const std::string& outputFolder,
        const std::string& filePattern,
        bool useBlending,
        int startIdx,
        int maxFrames,
        const StitchConfig& unifiedConfig,
        const std::string& serialNumber,
        const std::string& subprojectType
    ) {
        // Convert unified config to 4K-specific config
        // Note: We need to use the 4K pipeline's StitchConfig type
        // The 4K pipeline has its own StitchConfig in stitching_4k/config.h

        // Create 4K config with appropriate defaults
        // We need to use the 4K pipeline's StitchConfig type, which is different from the unified one
        // Let's create it using the 4K pipeline's constructor and set the values manually

        // Since both configs have the same name but are in different namespaces/files,
        // we need to be careful about which one we're using
        // The included header stitching_4k/config.h defines the 4K version

        // Create a 4K config and copy values from unified config
        auto config4k = StitchConfig(); // This will use the 4K version from the included header
        config4k.resizeScale = unifiedConfig.resizeScale;
        config4k.jpegQuality = unifiedConfig.jpegQuality;
        config4k.verbose = unifiedConfig.verbose;
        config4k.resolutionMode = "4K";

        // Set 4K-specific defaults
        config4k.stripWidth = unifiedConfig.stripWidth > 0 ? unifiedConfig.stripWidth : 20;
        config4k.templateMatchPrecision = unifiedConfig.templateMatchPrecision > 0 ? unifiedConfig.templateMatchPrecision : 1;

        // Apply 4K-specific parameters if the config supports it
        try {
            config4k.applyResolutionParams();
        } catch (...) {
            // If method doesn't exist, continue with manual settings
        }

        // Call the 4K pipeline function
        return stitchTireSurfaceWithOpticalFlow(
            inputFolder, outputFolder, filePattern,
            useBlending, startIdx, maxFrames, config4k,
            serialNumber, subprojectType
        );
    }

    cv::Mat enhancePanorama(
        const cv::Mat& panorama,
        const std::string& outputPath,
        const StitchConfig& unifiedConfig
    ) {
        // Convert unified config to 4K-specific config
        auto config4k = StitchConfig(); // This will use the 4K version from the included header
        config4k.resizeScale = unifiedConfig.resizeScale;
        config4k.jpegQuality = unifiedConfig.jpegQuality;
        config4k.verbose = unifiedConfig.verbose;
        config4k.resolutionMode = "4K";

        // Call the 4K pipeline enhancement function
        // Note: This function is defined in stitching_4k/image_blending.cpp
        return ::enhancePanorama(panorama, outputPath, config4k);
    }
}
