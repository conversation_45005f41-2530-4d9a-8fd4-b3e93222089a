#include "pipeline_4k_wrapper.h"

// Include 4K pipeline headers
#include "../stitching_4k/panorama_processor.h"
#include "../stitching_4k/config.h"

namespace Pipeline4K {
    cv::Mat stitchTireSurface(
        const std::string& inputFolder,
        const std::string& outputFolder,
        const std::string& filePattern,
        bool useBlending,
        int startIdx,
        int maxFrames,
        const StitchConfig& unifiedConfig,
        const std::string& serialNumber,
        const std::string& subprojectType
    ) {
        // Convert unified config to 4K-specific config
        // Note: We need to use the 4K pipeline's StitchConfig type
        // Since both have the same name, we'll create it directly
        
        // Create 4K config with appropriate defaults
        StitchConfig config4k;
        config4k.resizeScale = unifiedConfig.resizeScale;
        config4k.jpegQuality = unifiedConfig.jpegQuality;
        config4k.verbose = unifiedConfig.verbose;
        config4k.resolutionMode = "4K";
        
        // Set 4K-specific defaults
        config4k.stripWidth = unifiedConfig.stripWidth > 0 ? unifiedConfig.stripWidth : 20;
        config4k.templateMatchPrecision = unifiedConfig.templateMatchPrecision > 0 ? unifiedConfig.templateMatchPrecision : 1;
        
        // Apply 4K-specific parameters if the config supports it
        if (config4k.resolutionMode == "4K") {
            // This will set 4K-optimized parameters if the method exists
            try {
                config4k.applyResolutionParams();
            } catch (...) {
                // If method doesn't exist, continue with manual settings
            }
        }
        
        // Call the 4K pipeline function
        return stitchTireSurfaceWithOpticalFlow(
            inputFolder, outputFolder, filePattern,
            useBlending, startIdx, maxFrames, config4k,
            serialNumber, subprojectType
        );
    }

    cv::Mat enhancePanorama(
        const cv::Mat& panorama,
        const std::string& outputPath,
        const StitchConfig& unifiedConfig
    ) {
        // Convert unified config to 4K-specific config
        StitchConfig config4k;
        config4k.resizeScale = unifiedConfig.resizeScale;
        config4k.jpegQuality = unifiedConfig.jpegQuality;
        config4k.verbose = unifiedConfig.verbose;
        config4k.resolutionMode = "4K";
        
        // For now, return the input panorama as enhancement may not be implemented
        // in the 4K pipeline or may use a different function name
        // This can be updated once we know the exact function signature
        
        // Try to save the panorama to the output path
        try {
            cv::imwrite(outputPath, panorama);
        } catch (...) {
            // If save fails, continue
        }
        
        return panorama;
    }
}
