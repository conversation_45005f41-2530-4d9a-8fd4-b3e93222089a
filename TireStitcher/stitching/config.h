#ifndef UNIFIED_CONFIG_H
#define UNIFIED_CONFIG_H

#include <string>
#include <mutex>
#include <iostream>

// Mutex for thread-safe output
extern std::mutex coutMutex;

/**
 * Unified configuration structure for both 4K and 8K stitching pipelines.
 * This acts as a common interface that routes to the appropriate pipeline.
 */
struct StitchConfig {
    // Common parameters
    double resizeScale = 1.0;
    int jpegQuality = 90;
    bool verbose = false;

    // Pipeline-specific parameters
    int stripWidth = 0;  // 0 = auto-detect
    int templateMatchPrecision = 2;

    // Resolution mode for routing
    std::string resolutionMode = "8K";  // "4K" or "8K"

    // Constructor with defaults
    StitchConfig() = default;

    // Constructor with resolution mode
    StitchConfig(const std::string& mode) : resolutionMode(mode) {
        // Set mode-specific defaults
        if (mode == "4K") {
            stripWidth = 20;
            templateMatchPrecision = 1;
        } else {
            // 8K defaults (existing values)
            stripWidth = 0;
            templateMatchPrecision = 2;
        }
    }
};

// Thread-safe print function declaration
template<typename T>
void safePrint(const T& message, bool force = false, const StitchConfig& config = {});

// Thread-safe print function implementation
// Modified to not output to console
template<typename T>
void safePrint(const T& message, bool force, const StitchConfig& config) {
    // Disabled console output as per requirements
    // Only critical errors will be logged through the Python error handler
    return;
}

#endif // UNIFIED_CONFIG_H
