#include "pipeline_8k_wrapper.h"

// Include 8K pipeline headers
#include "../stitching_8k/panorama_processor.h"
#include "../stitching_8k/config.h"

namespace Pipeline8K {
    cv::Mat stitchTireSurface(
        const std::string& inputFolder,
        const std::string& outputFolder,
        const std::string& filePattern,
        bool useBlending,
        int startIdx,
        int maxFrames,
        const StitchConfig& unifiedConfig,
        const std::string& serialNumber,
        const std::string& subprojectType
    ) {
        // Convert unified config to 8K-specific config
        // Note: We need to use the 8K pipeline's StitchConfig type
        
        // Create 8K config
        StitchConfig config8k;
        config8k.resizeScale = unifiedConfig.resizeScale;
        config8k.jpegQuality = unifiedConfig.jpegQuality;
        config8k.verbose = unifiedConfig.verbose;
        config8k.resolutionMode = "8K";
        config8k.stripWidth = unifiedConfig.stripWidth;
        config8k.templateMatchPrecision = unifiedConfig.templateMatchPrecision;
        
        // Call the 8K pipeline function
        return stitchTireSurfaceWithOpticalFlow(
            inputFolder, outputFolder, filePattern,
            useBlending, startIdx, maxFrames, config8k,
            serialNumber, subprojectType
        );
    }

    cv::Mat enhancePanorama(
        const cv::Mat& panorama,
        const std::string& outputPath,
        const StitchConfig& unifiedConfig
    ) {
        // Convert unified config to 8K-specific config
        StitchConfig config8k;
        config8k.resizeScale = unifiedConfig.resizeScale;
        config8k.jpegQuality = unifiedConfig.jpegQuality;
        config8k.verbose = unifiedConfig.verbose;
        config8k.resolutionMode = "8K";
        
        // For now, return the input panorama as enhancement may not be implemented
        // or may use a different function name
        // This can be updated once we know the exact function signature
        
        // Try to save the panorama to the output path
        try {
            cv::imwrite(outputPath, panorama);
        } catch (...) {
            // If save fails, continue
        }
        
        return panorama;
    }
}
