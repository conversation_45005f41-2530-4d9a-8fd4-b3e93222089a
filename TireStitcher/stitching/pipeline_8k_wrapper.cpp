#include "pipeline_8k_wrapper.h"

// Include 8K pipeline headers
#include "../stitching_8k/panorama_processor.h"
#include "../stitching_8k/config.h"
#include "../stitching_8k/image_blending.h"
#include "../stitching_8k/debug_utils.h"

namespace Pipeline8K {
    cv::Mat stitchTireSurface(
        const std::string& inputFolder,
        const std::string& outputFolder,
        const std::string& filePattern,
        bool useBlending,
        int startIdx,
        int maxFrames,
        const StitchConfig& unifiedConfig,
        const std::string& serialNumber,
        const std::string& subprojectType
    ) {
        // Convert unified config to 8K-specific config
        // Note: We need to use the 8K pipeline's StitchConfig type
        // The 8K pipeline has its own StitchConfig in stitching_8k/config.h

        // Create 8K config using the 8K pipeline's constructor
        auto config8k = StitchConfig(); // This will use the 8K version from the included header
        config8k.resizeScale = unifiedConfig.resizeScale;
        config8k.jpegQuality = unifiedConfig.jpegQuality;
        config8k.verbose = unifiedConfig.verbose;
        config8k.resolutionMode = "8K";
        config8k.stripWidth = unifiedConfig.stripWidth;
        config8k.templateMatchPrecision = unifiedConfig.templateMatchPrecision;

        // Note: 8K pipeline doesn't have applyResolutionParams method
        // It uses the simpler configuration approach

        // Call the 8K pipeline function
        return stitchTireSurfaceWithOpticalFlow(
            inputFolder, outputFolder, filePattern,
            useBlending, startIdx, maxFrames, config8k,
            serialNumber, subprojectType
        );
    }

    cv::Mat enhancePanorama(
        const cv::Mat& panorama,
        const std::string& outputPath,
        const StitchConfig& unifiedConfig
    ) {
        // Convert unified config to 8K-specific config
        auto config8k = StitchConfig(); // This will use the 8K version from the included header
        config8k.resizeScale = unifiedConfig.resizeScale;
        config8k.jpegQuality = unifiedConfig.jpegQuality;
        config8k.verbose = unifiedConfig.verbose;
        config8k.resolutionMode = "8K";

        // Call the 8K pipeline enhancement function
        // Note: This function is defined in stitching_8k/image_blending.cpp
        return ::enhancePanorama(panorama, outputPath, config8k);
    }
}
