#ifndef STITCHING_FACTORY_H
#define STITCHING_FACTORY_H

#include <opencv2/opencv.hpp>
#include <string>
#include "config.h"

/**
 * Factory class for creating the appropriate stitching pipeline based on resolution mode.
 * This provides a unified interface that automatically routes to 4K or 8K implementations.
 */
class StitchingFactory {
public:
    /**
     * Create and execute the appropriate stitching pipeline based on resolution mode.
     * 
     * @param inputFolder Path to folder containing input frames
     * @param outputFolder Path to folder for output panorama
     * @param filePattern Pattern for matching input files (e.g., "IMG_*.JPG")
     * @param useBlending Whether to enable blending between frames
     * @param startIdx Starting frame index (for resuming)
     * @param maxFrames Maximum number of frames to process (-1 for all)
     * @param config Configuration including resolution mode
     * @param serialNumber Serial number for unique filenames
     * @param subprojectType Subproject type (frontal, left, right)
     * @return Resulting panorama image
     */
    static cv::Mat stitchTireSurface(
        const std::string& inputFolder,
        const std::string& outputFolder,
        const std::string& filePattern,
        bool useBlending,
        int startIdx,
        int maxFrames,
        const StitchConfig& config,
        const std::string& serialNumber = "",
        const std::string& subprojectType = ""
    );

    /**
     * Enhance panorama using the appropriate pipeline.
     * 
     * @param panorama Input panorama image
     * @param outputPath Path for enhanced output
     * @param config Configuration including resolution mode
     * @return Enhanced panorama image
     */
    static cv::Mat enhancePanorama(
        const cv::Mat& panorama,
        const std::string& outputPath,
        const StitchConfig& config
    );

private:
    /**
     * Determine resolution mode from input parameters or auto-detect.
     * 
     * @param inputFolder Path to input folder for analysis
     * @param config Configuration that may contain resolution mode
     * @return Resolved resolution mode ("4K" or "8K")
     */
    static std::string determineResolutionMode(
        const std::string& inputFolder,
        const StitchConfig& config
    );
};

#endif // STITCHING_FACTORY_H
